@extends('layouts.admin_minimal')

@section('title', 'Créer un rôle')

@push('styles')
<style>
    :root {
        --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --gradient-info: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --gradient-magic: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
        --gradient-ocean: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --gradient-sunset: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --gradient-aurora: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        --gradient-cosmic: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        --gradient-neon: linear-gradient(135deg, #00f2fe 0%, #4fac<PERSON> 50%, #667eea 100%);
        --shadow-soft: 0 15px 35px rgba(0, 0, 0, 0.1);
        --shadow-hover: 0 25px 50px rgba(0, 0, 0, 0.2);
        --shadow-glow: 0 0 40px rgba(102, 126, 234, 0.3);
        --shadow-neon: 0 0 60px rgba(79, 172, 254, 0.4);
        --border-radius: 25px;
        --border-radius-lg: 35px;
        --transition: all 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
        --transition-fast: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        --glass-bg: rgba(255, 255, 255, 0.25);
        --glass-border: rgba(255, 255, 255, 0.18);
        --glass-bg-strong: rgba(255, 255, 255, 0.35);
    }

    body {
        background: var(--gradient-cosmic);
        background-size: 400% 400%;
        animation: gradientShift 20s ease infinite;
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        overflow-x: hidden;
        position: relative;
    }

    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stars" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="white" opacity="0.3"><animate attributeName="opacity" values="0.3;0.8;0.3" dur="3s" repeatCount="indefinite"/></circle><circle cx="80" cy="30" r="0.5" fill="white" opacity="0.4"><animate attributeName="opacity" values="0.4;0.9;0.4" dur="2s" repeatCount="indefinite"/></circle><circle cx="40" cy="70" r="0.8" fill="white" opacity="0.2"><animate attributeName="opacity" values="0.2;0.7;0.2" dur="4s" repeatCount="indefinite"/></circle><circle cx="70" cy="80" r="0.6" fill="white" opacity="0.5"><animate attributeName="opacity" values="0.5;1;0.5" dur="2.5s" repeatCount="indefinite"/></circle><circle cx="10" cy="50" r="0.7" fill="white" opacity="0.3"><animate attributeName="opacity" values="0.3;0.9;0.3" dur="3.5s" repeatCount="indefinite"/></circle><circle cx="90" cy="70" r="0.4" fill="white" opacity="0.6"><animate attributeName="opacity" values="0.6;1;0.6" dur="2.2s" repeatCount="indefinite"/></circle></pattern></defs><rect width="100" height="100" fill="url(%23stars)"/></svg>');
        pointer-events: none;
        z-index: 1;
    }

    body::after {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
        pointer-events: none;
        z-index: 2;
        animation: floatingOrbs 25s ease-in-out infinite;
    }

    @keyframes floatingOrbs {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-20px) rotate(120deg); }
        66% { transform: translateY(10px) rotate(240deg); }
    }

    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .page-header {
        background: var(--glass-bg-strong);
        backdrop-filter: blur(25px);
        border: 2px solid var(--glass-border);
        color: white;
        padding: 4rem 0;
        margin: -1.5rem -1.5rem 4rem -1.5rem;
        border-radius: 0 0 60px 60px;
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-soft), var(--shadow-neon);
        z-index: 10;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--gradient-neon);
        opacity: 0.9;
        z-index: -2;
    }

    .page-header::after {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, transparent 70%);
        animation: rotate 25s linear infinite;
        z-index: -1;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .page-header h1 {
        font-size: 3.5rem;
        font-weight: 900;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
        background: linear-gradient(135deg, #ffffff 0%, #e0f2fe 50%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: textGlow 4s ease-in-out infinite alternate;
        letter-spacing: -1px;
    }

    @keyframes textGlow {
        from {
            filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.6));
            transform: scale(1);
        }
        to {
            filter: drop-shadow(0 0 30px rgba(255, 255, 255, 0.9));
            transform: scale(1.02);
        }
    }

    .page-header p {
        font-size: 1.2rem;
        opacity: 0.95;
        margin: 1rem 0 0 0;
        position: relative;
        z-index: 2;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }

    .btn-back {
        background: var(--glass-bg);
        backdrop-filter: blur(15px);
        border: 2px solid var(--glass-border);
        color: white;
        padding: 15px 30px;
        border-radius: 20px;
        font-weight: 700;
        font-size: 1.1rem;
        transition: var(--transition);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        position: relative;
        z-index: 2;
        overflow: hidden;
    }

    .btn-back::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.4) 100%);
        transition: left 0.5s ease;
        z-index: -1;
    }

    .btn-back:hover {
        color: white;
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
        border-color: rgba(255, 255, 255, 0.6);
    }

    .btn-back:hover::before {
        left: 0;
    }

    .creation-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 0 1rem;
        position: relative;
        z-index: 5;
        opacity: 1;
        visibility: visible;
    }

    .creation-card {
        background: var(--glass-bg-strong);
        backdrop-filter: blur(30px);
        border: 2px solid var(--glass-border);
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-soft), 0 0 50px rgba(102, 126, 234, 0.2);
        overflow: hidden;
        transition: var(--transition);
        position: relative;
        z-index: 10;
        margin: 2rem 0;
        opacity: 1;
        visibility: visible;
    }

    .creation-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
        z-index: -1;
    }

    .creation-card:hover {
        box-shadow: var(--shadow-hover), var(--shadow-neon);
        transform: translateY(-12px) scale(1.03);
        border-color: rgba(79, 172, 254, 0.5);
    }

    .card-header-modern {
        background: var(--gradient-neon);
        color: white;
        padding: 4rem;
        text-align: center;
        position: relative;
        overflow: hidden;
        border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    }

    .card-header-modern::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: conic-gradient(from 0deg, rgba(255,255,255,0.1), rgba(255,255,255,0.4), rgba(255,255,255,0.1));
        animation: shimmer 6s linear infinite;
    }

    .card-header-modern::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, transparent 0%, rgba(255,255,255,0.15) 50%, transparent 100%);
        animation: wave 4s ease-in-out infinite;
    }

    @keyframes shimmer {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    @keyframes wave {
        0%, 100% { transform: translateX(-100%); }
        50% { transform: translateX(100%); }
    }

    .card-header-modern h2 {
        font-size: 2.2rem;
        font-weight: 900;
        color: #000000;
        margin: 0;
        position: relative;
        z-index: 3;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    @keyframes titlePulse {
        from { transform: scale(1); }
        to { transform: scale(1.05); }
    }

    .card-header-modern p {
        margin: 1rem 0 0 0;
        color: #333333;
        position: relative;
        z-index: 3;
        font-size: 1.1rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .form-section {
        padding: 3rem;
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
    }

    .form-group-modern {
        margin-bottom: 2.5rem;
        position: relative;
    }

    .form-label-modern {
        font-weight: 800;
        color: #000000;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        font-size: 1.2rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .form-label-modern::before {
        content: '';
        position: absolute;
        left: -10px;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 100%;
        background: var(--gradient-success);
        border-radius: 2px;
        animation: labelGlow 2s ease-in-out infinite alternate;
    }

    @keyframes labelGlow {
        from { opacity: 0.5; }
        to { opacity: 1; }
    }

    .form-label-modern i {
        margin-right: 0.8rem;
        color: #ffd700;
        font-size: 1.3rem;
        animation: iconSpin 3s linear infinite;
    }

    @keyframes iconSpin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .form-control-modern {
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 25px;
        padding: 1.5rem 2rem;
        font-size: 1.1rem;
        transition: var(--transition);
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(20px);
        font-weight: 600;
        color: #000000;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .form-control-modern::placeholder {
        color: rgba(0, 0, 0, 0.5);
        font-weight: 500;
        font-style: italic;
    }

    .form-control-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
    }

    .form-control-modern:focus {
        border-color: #4facfe;
        box-shadow: 0 0 0 4px rgba(79, 172, 254, 0.3), 0 0 30px rgba(79, 172, 254, 0.4);
        background: rgba(255, 255, 255, 0.95);
        transform: translateY(-5px) scale(1.03);
        color: #000000;
    }

    .form-control-modern:focus::before {
        left: 100%;
    }

    .form-control-modern.is-invalid {
        border-color: #ff6b6b;
        background: rgba(255, 107, 107, 0.1);
        box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.2);
        color: #000000;
    }

    .form-control-modern.is-valid {
        border-color: #51cf66;
        background: rgba(81, 207, 102, 0.1);
        box-shadow: 0 0 0 3px rgba(81, 207, 102, 0.2);
        color: #000000;
    }

    /* Styles spécifiques pour les éléments select */
    select.form-control-modern {
        color: #000000;
        background: rgba(255, 255, 255, 0.9);
    }

    select.form-control-modern option {
        color: #000000;
        background: #ffffff;
        padding: 10px;
    }

    select.form-control-modern:focus {
        color: #000000;
        background: rgba(255, 255, 255, 0.95);
    }

    /* Styles pour les textarea */
    textarea.form-control-modern {
        color: #000000;
        background: rgba(255, 255, 255, 0.9);
        resize: vertical;
        min-height: 120px;
    }

    textarea.form-control-modern:focus {
        color: #000000;
        background: rgba(255, 255, 255, 0.95);
    }

    .invalid-feedback {
        color: #e53e3e;
        font-weight: 600;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
    }

    .invalid-feedback::before {
        content: '⚠️';
        margin-right: 0.5rem;
    }

    .permissions-section {
        background: var(--glass-bg-strong);
        backdrop-filter: blur(25px);
        border-radius: var(--border-radius-lg);
        padding: 4rem;
        margin-top: 3rem;
        border: 2px solid var(--glass-border);
        position: relative;
        overflow: hidden;
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .permissions-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--gradient-aurora);
        opacity: 0.15;
        z-index: -1;
    }

    .permissions-header {
        text-align: center;
        margin-bottom: 3rem;
        position: relative;
        z-index: 2;
    }

    .permissions-header h3 {
        color: #000000;
        font-weight: 900;
        margin: 0;
        font-size: 2rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    @keyframes headerFloat {
        from { transform: translateY(0px); }
        to { transform: translateY(-5px); }
    }

    .permissions-header p {
        color: #333333;
        margin: 1rem 0 0 0;
        font-size: 1.1rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .permissions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
        position: relative;
        z-index: 2;
    }

    .permission-group {
        background: var(--glass-bg-strong);
        backdrop-filter: blur(20px);
        border-radius: var(--border-radius);
        padding: 2.5rem;
        box-shadow: var(--shadow-soft), 0 0 20px rgba(79, 172, 254, 0.1);
        border: 2px solid var(--glass-border);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .permission-group::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
        z-index: -1;
    }

    .permission-group:hover {
        border-color: #4facfe;
        transform: translateY(-8px) scale(1.05);
        box-shadow: var(--shadow-hover), var(--shadow-neon);
    }

    .permission-group h4 {
        color: #000000;
        font-weight: 900;
        margin: 0 0 1.5rem 0;
        font-size: 1.4rem;
        display: flex;
        align-items: center;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .permission-group h4::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 0;
        width: 50px;
        height: 3px;
        background: var(--gradient-success);
        border-radius: 2px;
        animation: underlineGlow 2s ease-in-out infinite alternate;
    }

    @keyframes underlineGlow {
        from { width: 30px; opacity: 0.7; }
        to { width: 70px; opacity: 1; }
    }

    .permission-group h4 i {
        margin-right: 0.8rem;
        color: #ffd700;
        font-size: 1.5rem;
        animation: iconBounce 2s ease-in-out infinite;
    }

    @keyframes iconBounce {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-3px); }
    }

    .permission-item {
        display: flex;
        align-items: center;
        padding: 1.3rem;
        margin: 1rem 0;
        border-radius: 20px;
        transition: var(--transition-fast);
        cursor: pointer;
        border: 2px solid transparent;
        background: rgba(255, 255, 255, 0.08);
        backdrop-filter: blur(15px);
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        color: #000000 !important;
    }

    .permission-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(79, 172, 254, 0.2), transparent);
        transition: left 0.6s ease;
    }

    .permission-item:hover {
        background: rgba(79, 172, 254, 0.25);
        border-color: #4facfe;
        transform: translateX(8px) scale(1.02);
        box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
    }

    .permission-item:hover::before {
        left: 100%;
    }

    .permission-item.selected {
        background: var(--gradient-success);
        color: white;
        border-color: #38ef7d;
        box-shadow: 0 10px 30px rgba(56, 239, 125, 0.4);
        animation: selectedPulse 3s ease-in-out infinite;
    }

    @keyframes selectedPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.02); }
    }

    .permission-checkbox {
        width: 24px;
        height: 24px;
        margin-right: 1.2rem;
        cursor: pointer;
        accent-color: #ffd700;
        transform: scale(1.2);
        transition: var(--transition);
    }

    .permission-checkbox:checked {
        animation: checkboxPop 0.3s ease-out;
    }

    @keyframes checkboxPop {
        0% { transform: scale(1.2); }
        50% { transform: scale(1.5); }
        100% { transform: scale(1.2); }
    }

    .permission-label {
        font-weight: 700;
        cursor: pointer;
        flex: 1;
        color: #000000 !important;
        text-shadow: none;
        font-size: 1.1rem;
    }

    .permission-description {
        font-size: 0.95rem;
        opacity: 0.8;
        margin-top: 0.3rem;
        color: #333333 !important;
        font-style: italic;
    }

    .actions-section {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        padding: 3rem;
        text-align: center;
        border-top: 2px solid var(--glass-border);
        position: relative;
        overflow: hidden;
    }

    .actions-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--gradient-magic);
        opacity: 0.1;
        z-index: -1;
    }

    .btn-create {
        background: var(--gradient-neon);
        border: none;
        color: white;
        padding: 2rem 5rem;
        border-radius: 35px;
        font-weight: 900;
        font-size: 1.4rem;
        transition: var(--transition);
        box-shadow: var(--shadow-soft), var(--shadow-neon);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 2px;
        min-width: 280px;
        justify-content: center;
    }

    .btn-create::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        transition: left 0.6s ease;
        z-index: 0;
    }

    .btn-create::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: radial-gradient(circle, rgba(255,255,255,0.4) 0%, transparent 70%);
        transition: all 0.6s ease;
        transform: translate(-50%, -50%);
        z-index: 1;
    }

    .btn-create:hover {
        color: white;
        transform: translateY(-8px) scale(1.08);
        box-shadow: var(--shadow-hover), 0 0 80px rgba(79, 172, 254, 0.8);
        animation: buttonPulse 1.5s ease-in-out infinite;
    }

    @keyframes buttonPulse {
        0%, 100% { box-shadow: var(--shadow-hover), 0 0 50px rgba(17, 153, 142, 0.6); }
        50% { box-shadow: var(--shadow-hover), 0 0 70px rgba(17, 153, 142, 0.8); }
    }

    .btn-create:hover::before {
        left: 0;
    }

    .btn-create:hover::after {
        width: 300px;
        height: 300px;
    }

    .btn-create i,
    .btn-create span {
        position: relative;
        z-index: 2;
    }

    .btn-create i {
        margin-right: 1rem;
        font-size: 1.5rem;
        animation: iconRotate 3s linear infinite;
    }

    @keyframes iconRotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Styles pour les boutons d'action des permissions */
    .btn-permission-action {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(15px);
        border: 2px solid rgba(79, 172, 254, 0.5);
        color: #000000;
        padding: 12px 24px;
        border-radius: 20px;
        font-weight: 700;
        font-size: 1rem;
        transition: var(--transition-fast);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        position: relative;
        overflow: hidden;
        cursor: pointer;
        margin: 5px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-permission-action::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .btn-select-all:hover {
        background: rgba(56, 239, 125, 0.9);
        border-color: #38ef7d;
        color: #ffffff;
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(56, 239, 125, 0.4);
    }

    .btn-deselect-all:hover {
        background: rgba(245, 87, 108, 0.9);
        border-color: #f5576c;
        color: #ffffff;
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(245, 87, 108, 0.4);
    }

    .btn-permission-action:hover::before {
        left: 100%;
    }

    .fade-in-up {
        opacity: 1;
        transform: translateY(0);
        transition: var(--transition);
    }

    .fade-in-up.animate {
        opacity: 0;
        transform: translateY(30px);
        animation: fadeInUp 0.8s ease-out forwards;
    }

    @keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .pulse-glow {
        animation: pulseGlow 2s ease-in-out infinite;
    }

    @keyframes pulseGlow {
        0%, 100% {
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        50% {
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
    }

    /* Ajout d'un indicateur de progression */
    .progress-indicator {
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 4px;
        background: var(--gradient-neon);
        z-index: 9999;
        transition: width 0.3s ease;
        box-shadow: 0 0 10px rgba(79, 172, 254, 0.6);
    }

    /* Animation pour les éléments qui apparaissent */
    .slide-in-right {
        opacity: 1;
        transform: translateX(0);
        transition: var(--transition);
    }

    .slide-in-right.animate {
        opacity: 0;
        transform: translateX(50px);
        animation: slideInRight 0.8s ease-out forwards;
    }

    @keyframes slideInRight {
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* Styles pour les alertes */
    .alert {
        background: var(--glass-bg-strong);
        backdrop-filter: blur(20px);
        border: 2px solid;
        border-radius: var(--border-radius);
        color: white;
        margin-bottom: 2rem;
        position: relative;
        z-index: 15;
    }

    .alert-success {
        border-color: rgba(56, 161, 105, 0.5);
        background: linear-gradient(135deg, rgba(56, 161, 105, 0.2), rgba(56, 161, 105, 0.1));
    }

    .alert-danger {
        border-color: rgba(229, 62, 62, 0.5);
        background: linear-gradient(135deg, rgba(229, 62, 62, 0.2), rgba(229, 62, 62, 0.1));
    }

    .alert-warning {
        border-color: rgba(255, 193, 7, 0.5);
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 193, 7, 0.1));
    }

    /* Force visibility for all form elements */
    .form-section,
    .form-group-modern,
    .permissions-section,
    .permissions-header,
    .permission-group,
    .permission-item,
    .btn-create,
    .btn-permission-action,
    .btn-select-all,
    .btn-deselect-all {
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
        position: relative !important;
        z-index: 10 !important;
    }

    .permission-item {
        display: flex !important;
    }

    .btn-permission-action {
        display: inline-block !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .page-header {
            padding: 2rem 0;
            margin: -1rem -1rem 2rem -1rem;
            border-radius: 0 0 40px 40px;
        }

        .page-header h1 {
            font-size: 2.5rem;
        }

        .form-section, .permissions-section {
            padding: 2rem;
        }

        .permissions-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .btn-create {
            padding: 1.5rem 3rem;
            font-size: 1.2rem;
            min-width: 250px;
        }

        .btn-permission-action {
            padding: 10px 20px;
            font-size: 0.9rem;
        }

        .creation-card {
            border-radius: var(--border-radius);
        }
    }

    @media (max-width: 480px) {
        .page-header h1 {
            font-size: 2rem;
        }

        .card-header-modern {
            padding: 2.5rem 1.5rem;
        }

        .btn-create {
            padding: 1.2rem 2rem;
            font-size: 1rem;
            min-width: 200px;
        }
    }
</style>
@endpush

@section('content')
<!-- Indicateur de progression -->
<div class="progress-indicator" id="progressIndicator"></div>

<!-- Messages d'alerte -->
@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if($errors->any())
    <div class="alert alert-danger alert-dismissible fade show shadow-lg" role="alert" style="background: linear-gradient(135deg, #ff6b6b, #ee5a52); border: none; color: white; border-radius: 15px; margin-bottom: 2rem;">
        <i class="fas fa-exclamation-triangle me-2" style="font-size: 1.2rem;"></i>
        <strong>Erreurs de validation :</strong>
        <ul class="mb-0 mt-2" style="list-style: none; padding-left: 0;">
            @foreach($errors->all() as $error)
                <li style="padding: 0.3rem 0; border-left: 3px solid rgba(255,255,255,0.5); padding-left: 1rem; margin: 0.5rem 0;">
                    <i class="fas fa-times-circle me-2"></i>{{ $error }}
                </li>
            @endforeach
        </ul>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert" style="filter: brightness(0) invert(1);"></button>
    </div>
@endif

<div class="container-fluid">
    <!-- Header moderne avec gradient -->
    <div class="page-header fade-in-up">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1>
                    <i class="fas fa-user-plus me-3"></i>
                    Créer un Nouveau Rôle
                </h1>
                <p>Définissez les permissions et configurez un nouveau rôle pour votre système</p>
            </div>
            <a href="{{ route('admin.roles.index') }}" class="btn-back slide-in-right">
                <i class="fas fa-arrow-left me-2"></i>
                Retour aux Rôles
            </a>
        </div>
    </div>

    <div class="creation-container">
        <div class="creation-card fade-in-up pulse-glow">
            <!-- Header de la carte -->
            <div class="card-header-modern">
                <h2>
                    <i class="fas fa-shield-alt me-2"></i>
                    Configuration du Rôle
                </h2>
                <p>Remplissez les informations ci-dessous pour créer un nouveau rôle</p>
            </div>

            <form action="{{ route('admin.roles.store') }}" method="POST" id="roleForm">
                @csrf

                <!-- Section des informations de base -->
                <div class="form-section">
                    <div class="form-group-modern">
                        <label for="name" class="form-label-modern">
                            <i class="fas fa-tag"></i>
                            Nom du Rôle
                        </label>
                        <input type="text"
                               class="form-control-modern @error('name') is-invalid @enderror"
                               id="name"
                               name="name"
                               value="{{ old('name') }}"
                               placeholder="Ex: Gestionnaire de contenu, Analyste, Support..."
                               required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted mt-2">
                            <i class="fas fa-info-circle"></i>
                            Rôles existants :
                            @php
                                $existingRoles = \Spatie\Permission\Models\Role::pluck('name')->toArray();
                            @endphp
                            {{ implode(', ', $existingRoles) }}
                        </small>
                    </div>

                    <div class="form-group-modern">
                        <label for="description" class="form-label-modern">
                            <i class="fas fa-align-left"></i>
                            Description (Optionnel)
                        </label>
                        <textarea class="form-control-modern @error('description') is-invalid @enderror"
                                  id="description"
                                  name="description"
                                  rows="3"
                                  placeholder="Décrivez brièvement les responsabilités de ce rôle...">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group-modern">
                                <label for="color" class="form-label-modern">
                                    <i class="fas fa-palette"></i>
                                    Couleur du Rôle
                                </label>
                                <input type="color"
                                       class="form-control-modern @error('color') is-invalid @enderror"
                                       id="color"
                                       name="color"
                                       value="{{ old('color', '#667eea') }}"
                                       required>
                                @error('color')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group-modern">
                                <label for="priority_level" class="form-label-modern">
                                    <i class="fas fa-star"></i>
                                    Niveau de Priorité
                                </label>
                                <select class="form-control-modern @error('priority_level') is-invalid @enderror"
                                        id="priority_level"
                                        name="priority_level"
                                        required>
                                    <option value="">Sélectionnez un niveau</option>
                                    @for($i = 1; $i <= 10; $i++)
                                        <option value="{{ $i }}" {{ old('priority_level') == $i ? 'selected' : '' }}>
                                            Niveau {{ $i }} {{ $i <= 3 ? '(Faible)' : ($i <= 7 ? '(Moyen)' : '(Élevé)') }}
                                        </option>
                                    @endfor
                                </select>
                                @error('priority_level')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section des permissions -->
                <div class="permissions-section">
                    <div class="permissions-header">
                        <h3>
                            <i class="fas fa-key me-2"></i>
                            Sélection des Permissions
                        </h3>
                        <p>Choisissez les permissions que ce rôle pourra exercer</p>
                        <div class="mt-4">
                            <button type="button" class="btn-permission-action btn-select-all me-3" onclick="selectAllPermissions()">
                                <i class="fas fa-check-double me-2"></i>Tout Sélectionner
                            </button>
                            <button type="button" class="btn-permission-action btn-deselect-all" onclick="deselectAllPermissions()">
                                <i class="fas fa-times me-2"></i>Tout Désélectionner
                            </button>
                        </div>
                    </div>

                    <div class="permissions-grid">
                        @if(isset($permissionGroups) && $permissionGroups)
                            @foreach($permissionGroups as $group => $groupPermissions)
                                <div class="permission-group">
                                    <h4>
                                        <i class="fas fa-{{ $group === 'users' ? 'users' : ($group === 'roles' ? 'user-shield' : ($group === 'content' ? 'file-alt' : 'cog')) }}"></i>
                                        {{ ucfirst($group) }}
                                    </h4>
                                    @foreach($groupPermissions as $permission)
                                        <div class="permission-item" onclick="togglePermission({{ $permission->id }})">
                                            <input type="checkbox"
                                                   class="permission-checkbox"
                                                   id="permission_{{ $permission->id }}"
                                                   name="permissions[]"
                                                   value="{{ $permission->id }}"
                                                   {{ in_array($permission->id, old('permissions', [])) ? 'checked' : '' }}>
                                            <div class="permission-label">
                                                {{ $permission->name }}
                                                <div class="permission-description">
                                                    Permission pour {{ $permission->name }}
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @endforeach
                        @elseif(isset($permissions) && $permissions)
                            @foreach($permissions as $permission)
                                <div class="permission-group">
                                    <div class="permission-item" onclick="togglePermission({{ $permission->id }})">
                                        <input type="checkbox"
                                               class="permission-checkbox"
                                               id="permission_{{ $permission->id }}"
                                               name="permissions[]"
                                               value="{{ $permission->id }}"
                                               {{ in_array($permission->id, old('permissions', [])) ? 'checked' : '' }}>
                                        <div class="permission-label">
                                            {{ $permission->name }}
                                            <div class="permission-description">
                                                Permission pour {{ $permission->name }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Aucune permission disponible. Veuillez contacter l'administrateur.
                            </div>
                        @endif
                    </div>

                    @error('permissions')
                        <div class="invalid-feedback d-block mt-3">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Section des actions -->
                <div class="actions-section">
                    <button type="submit" class="btn-create">
                        <i class="fas fa-save"></i>
                        <span>Créer le Rôle</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Indicateur de progression
    updateProgressIndicator();

    // Animation d'entrée progressive
    setTimeout(() => {
        const elements = document.querySelectorAll('.fade-in-up');
        elements.forEach((el, index) => {
            el.classList.add('animate');
            el.style.animationDelay = `${index * 0.1}s`;
        });

        // Animation pour les éléments slide-in-right
        const slideElements = document.querySelectorAll('.slide-in-right');
        slideElements.forEach((el, index) => {
            el.classList.add('animate');
            el.style.animationDelay = `${(index + elements.length) * 0.1}s`;
        });
    }, 100);

    // Validation en temps réel
    const nameInput = document.getElementById('name');
    const form = document.getElementById('roleForm');

    nameInput.addEventListener('input', function() {
        validateRoleName(this.value);
        updateProgressIndicator();
    });

    // Compteur de permissions sélectionnées
    updatePermissionCounter();

    // Écouteurs pour les checkboxes
    const checkboxes = document.querySelectorAll('.permission-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updatePermissionItem(this);
            updatePermissionCounter();
            updateProgressIndicator();
        });
    });

    // Validation du formulaire
    form.addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        } else {
            // Animation de soumission
            showSubmissionAnimation();
        }
    });

    // Effet de couleur dynamique
    const colorInput = document.getElementById('color');
    if (colorInput) {
        colorInput.addEventListener('change', function() {
            updateColorPreview(this.value);
            updateProgressIndicator();
        });
        // Initialiser la prévisualisation de couleur
        updateColorPreview(colorInput.value);
    }

    // Écouteur pour le niveau de priorité
    const priorityInput = document.getElementById('priority_level');
    if (priorityInput) {
        priorityInput.addEventListener('change', function() {
            updateProgressIndicator();
        });
    }
});

// Fonction pour basculer une permission
function togglePermission(permissionId) {
    const checkbox = document.getElementById(`permission_${permissionId}`);
    checkbox.checked = !checkbox.checked;
    updatePermissionItem(checkbox);
    updatePermissionCounter();
}

// Mettre à jour l'apparence d'un élément de permission
function updatePermissionItem(checkbox) {
    const permissionItem = checkbox.closest('.permission-item');
    if (checkbox.checked) {
        permissionItem.classList.add('selected');
    } else {
        permissionItem.classList.remove('selected');
    }
}

// Sélectionner toutes les permissions
function selectAllPermissions() {
    const checkboxes = document.querySelectorAll('.permission-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
        updatePermissionItem(checkbox);
    });
    updatePermissionCounter();

    // Animation de feedback
    showFeedback('Toutes les permissions sélectionnées', 'success');
}

// Désélectionner toutes les permissions
function deselectAllPermissions() {
    const checkboxes = document.querySelectorAll('.permission-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
        updatePermissionItem(checkbox);
    });
    updatePermissionCounter();

    // Animation de feedback
    showFeedback('Toutes les permissions désélectionnées', 'info');
}

// Mettre à jour le compteur de permissions
function updatePermissionCounter() {
    const checkedBoxes = document.querySelectorAll('.permission-checkbox:checked');
    const totalBoxes = document.querySelectorAll('.permission-checkbox');

    // Créer ou mettre à jour le compteur
    let counter = document.getElementById('permission-counter');
    if (!counter) {
        counter = document.createElement('div');
        counter.id = 'permission-counter';
        counter.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 700;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            z-index: 1000;
            transition: all 0.3s ease;
        `;
        document.body.appendChild(counter);
    }

    counter.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${checkedBoxes.length} / ${totalBoxes.length} permissions
    `;

    // Animation du compteur
    counter.style.transform = 'scale(1.1)';
    setTimeout(() => {
        counter.style.transform = 'scale(1)';
    }, 200);
}

// Validation du nom du rôle
function validateRoleName(name) {
    const nameInput = document.getElementById('name');
    const feedback = nameInput.parentNode.querySelector('.validation-feedback') || createValidationFeedback(nameInput.parentNode);
    const existingRoles = @json(\Spatie\Permission\Models\Role::pluck('name')->toArray());

    if (name.length < 3) {
        nameInput.classList.add('is-invalid');
        feedback.textContent = 'Le nom doit contenir au moins 3 caractères';
        feedback.className = 'validation-feedback invalid';
    } else if (name.length > 50) {
        nameInput.classList.add('is-invalid');
        feedback.textContent = 'Le nom ne peut pas dépasser 50 caractères';
        feedback.className = 'validation-feedback invalid';
    } else if (existingRoles.includes(name.toLowerCase())) {
        nameInput.classList.add('is-invalid');
        feedback.textContent = 'Ce nom de rôle existe déjà';
        feedback.className = 'validation-feedback invalid';
    } else {
        nameInput.classList.remove('is-invalid');
        nameInput.classList.add('is-valid');
        feedback.textContent = 'Nom valide ✓';
        feedback.className = 'validation-feedback valid';
    }
}

// Créer un élément de feedback de validation
function createValidationFeedback(parent) {
    const feedback = document.createElement('div');
    feedback.className = 'validation-feedback';
    feedback.style.cssText = `
        margin-top: 0.5rem;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    `;
    parent.appendChild(feedback);
    return feedback;
}

// Mettre à jour la prévisualisation de couleur
function updateColorPreview(color) {
    const preview = document.getElementById('color-preview') || createColorPreview();
    preview.style.background = color;
    preview.style.boxShadow = `0 4px 15px ${color}40`;
}

// Créer la prévisualisation de couleur
function createColorPreview() {
    const colorInput = document.getElementById('color');
    const preview = document.createElement('div');
    preview.id = 'color-preview';
    preview.style.cssText = `
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 3px solid white;
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        transition: all 0.3s ease;
    `;

    colorInput.parentNode.style.position = 'relative';
    colorInput.parentNode.appendChild(preview);
    return preview;
}

// Validation complète du formulaire
function validateForm() {
    const name = document.getElementById('name').value;
    const color = document.getElementById('color').value;
    const priorityLevel = document.getElementById('priority_level').value;
    const checkedPermissions = document.querySelectorAll('.permission-checkbox:checked');

    // Validation du nom
    if (name.length < 3) {
        showFeedback('Le nom du rôle doit contenir au moins 3 caractères', 'error');
        document.getElementById('name').focus();
        return false;
    }

    // Validation de la couleur
    if (!color) {
        showFeedback('Veuillez sélectionner une couleur pour le rôle', 'error');
        document.getElementById('color').focus();
        return false;
    }

    // Validation du niveau de priorité
    if (!priorityLevel) {
        showFeedback('Veuillez sélectionner un niveau de priorité', 'error');
        document.getElementById('priority_level').focus();
        return false;
    }

    // Validation des permissions
    if (checkedPermissions.length === 0) {
        showFeedback('Veuillez sélectionner au moins une permission', 'error');
        return false;
    }

    // Animation de succès
    showFeedback('Création du rôle en cours...', 'success');
    return true;
}

// Afficher un message de feedback
function showFeedback(message, type) {
    // Supprimer les anciens feedbacks
    const existingFeedback = document.querySelector('.feedback-message');
    if (existingFeedback) {
        existingFeedback.remove();
    }

    const feedback = document.createElement('div');
    feedback.className = 'feedback-message';
    feedback.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
        ${message}
    `;

    const colors = {
        success: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
        error: 'linear-gradient(135deg, #e53e3e 0%, #f56565 100%)',
        info: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
    };

    feedback.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${colors[type]};
        color: white;
        padding: 15px 25px;
        border-radius: 25px;
        font-weight: 600;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        z-index: 1001;
        animation: slideInDown 0.5s ease-out;
    `;

    document.body.appendChild(feedback);

    // Supprimer automatiquement après 3 secondes
    setTimeout(() => {
        feedback.style.animation = 'slideOutUp 0.5s ease-in';
        setTimeout(() => feedback.remove(), 500);
    }, 3000);
}

// Mettre à jour l'indicateur de progression
function updateProgressIndicator() {
    const nameInput = document.getElementById('name');
    const colorInput = document.getElementById('color');
    const priorityInput = document.getElementById('priority_level');
    const checkedPermissions = document.querySelectorAll('.permission-checkbox:checked');
    const totalPermissions = document.querySelectorAll('.permission-checkbox');

    let progress = 0;

    // 25% pour le nom du rôle
    if (nameInput && nameInput.value.length >= 3) {
        progress += 25;
    }

    // 15% pour la couleur
    if (colorInput && colorInput.value) {
        progress += 15;
    }

    // 15% pour le niveau de priorité
    if (priorityInput && priorityInput.value) {
        progress += 15;
    }

    // 45% pour les permissions (au moins une sélectionnée)
    if (checkedPermissions.length > 0) {
        progress += 45 * (checkedPermissions.length / totalPermissions.length);
    }

    const progressBar = document.getElementById('progressIndicator');
    if (progressBar) {
        progressBar.style.width = progress + '%';

        // Changer la couleur selon le progrès
        if (progress < 30) {
            progressBar.style.background = 'linear-gradient(90deg, #ff6b6b, #ee5a52)';
        } else if (progress < 70) {
            progressBar.style.background = 'linear-gradient(90deg, #feca57, #ff9ff3)';
        } else {
            progressBar.style.background = 'var(--gradient-neon)';
        }
    }
}

// Animation de soumission
function showSubmissionAnimation() {
    const submitBtn = document.querySelector('.btn-create');
    if (submitBtn) {
        submitBtn.innerHTML = `
            <i class="fas fa-spinner fa-spin me-2"></i>
            <span>Création en cours...</span>
        `;
        submitBtn.style.background = 'var(--gradient-info)';
        submitBtn.disabled = true;
    }

    // Mettre la barre de progression à 100%
    const progressBar = document.getElementById('progressIndicator');
    if (progressBar) {
        progressBar.style.width = '100%';
    }
}

// Styles CSS pour les animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInDown {
        from {
            opacity: 0;
            transform: translateX(-50%) translateY(-100%);
        }
        to {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
    }

    @keyframes slideOutUp {
        from {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
        to {
            opacity: 0;
            transform: translateX(-50%) translateY(-100%);
        }
    }

    .validation-feedback.valid {
        color: #38a169;
    }

    .validation-feedback.invalid {
        color: #e53e3e;
    }

    .form-control-modern.is-valid {
        border-color: #38a169;
        background: rgba(56, 161, 105, 0.1);
        color: #000000;
    }

    .form-control-modern.is-invalid {
        border-color: #e53e3e;
        background: rgba(229, 62, 62, 0.1);
        color: #000000;
    }
`;
document.head.appendChild(style);
</script>
@endpush
