@extends('layouts.admin_minimal')

@section('title', 'Gestion des Rôles')

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/apexcharts@3.41.0/dist/apexcharts.min.css" rel="stylesheet">
<style>
    /* Variables CSS personnalisées */
    :root {
        --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --gradient-info: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
        --gradient-danger: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        --gradient-dark: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }

    /* Styles pour SweetAlert2 */
    .modern-swal-popup {
        border-radius: 20px !important;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15) !important;
    }

    .modern-swal-confirm-danger {
        background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
        border: none !important;
        border-radius: 12px !important;
        padding: 12px 24px !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
    }

    .modern-swal-confirm-danger:hover {
        background: linear-gradient(135deg, #b91c1c, #991b1b) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3) !important;
    }

    .modern-swal-cancel {
        background: #6b7280 !important;
        border: none !important;
        border-radius: 12px !important;
        padding: 12px 24px !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
    }

    .modern-swal-cancel:hover {
        background: #4b5563 !important;
        transform: translateY(-2px) !important;
    }

    .swal2-input {
        border-radius: 12px !important;
        border: 2px solid #e5e7eb !important;
        padding: 12px 16px !important;
        font-size: 16px !important;
        transition: all 0.3s ease !important;
    }

    .swal2-input:focus {
        border-color: #dc2626 !important;
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
        outline: none !important;
    }

    .swal2-validation-message {
        background: #fef2f2 !important;
        color: #dc2626 !important;
        border: 1px solid #fecaca !important;
        border-radius: 8px !important;
        padding: 8px 12px !important;
        margin-top: 8px !important;
        --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.1);
        --shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
        --border-radius: 20px;
        --transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    }

    /* Styles généraux */
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    /* Header moderne avec gradient */
    .page-header {
        background: var(--gradient-primary);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .page-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 1;
    }

    .page-header p {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0.5rem 0 0 0;
        position: relative;
        z-index: 1;
    }

    /* Cartes de statistiques modernes */
    .stats-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-soft);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
        border: none;
        height: 100%;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: var(--gradient-primary);
    }

    .stats-card:hover {
        transform: translateY(-10px);
        box-shadow: var(--shadow-hover);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
        position: relative;
    }

    .stats-icon.primary { background: var(--gradient-primary); }
    .stats-icon.success { background: var(--gradient-success); }
    .stats-icon.warning { background: var(--gradient-warning); }
    .stats-icon.info { background: var(--gradient-info); }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2d3748;
        margin: 0;
        line-height: 1;
    }

    .stats-label {
        color: #718096;
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: 0.5rem;
    }

    /* Cartes de rôles ultra-modernes */
    .role-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-soft);
        transition: var(--transition);
        border: none;
        overflow: hidden;
        position: relative;
        height: 100%;
    }

    .role-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 6px;
        background: var(--gradient-primary);
        transition: var(--transition);
    }

    .role-card:hover {
        transform: translateY(-15px) scale(1.02);
        box-shadow: var(--shadow-hover);
    }

    .role-card:hover::before {
        height: 8px;
    }

    .role-header {
        padding: 2rem 2rem 1rem 2rem;
        position: relative;
    }

    .role-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #2d3748;
        margin: 0 0 0.5rem 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .role-subtitle {
        color: #718096;
        font-size: 0.9rem;
        margin: 0;
    }

    .role-body {
        padding: 0 2rem 1rem 2rem;
    }

    .role-description {
        color: #4a5568;
        font-size: 0.95rem;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    /* Badges de permissions stylés */
    .permission-badge {
        display: inline-block;
        padding: 0.4rem 0.8rem;
        margin: 0.2rem;
        border-radius: 25px;
        font-size: 0.75rem;
        font-weight: 600;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .permission-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
    }

    .permission-badge:hover::before {
        left: 100%;
    }

    .permission-badge:hover {
        transform: scale(1.1) rotate(2deg);
    }

    .permission-badge.bg-primary { background: var(--gradient-primary) !important; }
    .permission-badge.bg-success { background: var(--gradient-success) !important; }
    .permission-badge.bg-warning { background: var(--gradient-warning) !important; }
    .permission-badge.bg-info { background: var(--gradient-info) !important; }
    .permission-badge.bg-secondary { background: var(--gradient-dark) !important; }

    /* Indicateurs d'activité animés */
    .activity-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
        position: relative;
        animation: pulse-glow 2s infinite;
    }

    .activity-very-active {
        background: #10B981;
        box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
    }
    .activity-active {
        background: #3B82F6;
        box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
    }
    .activity-moderately-active {
        background: #F59E0B;
        box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
    }
    .activity-rarely-used {
        background: #EF4444;
        box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
    }
    .activity-inactive {
        background: #6B7280;
        box-shadow: 0 0 10px rgba(107, 114, 128, 0.5);
    }

    @keyframes pulse-glow {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.6; }
    }

    /* Score de sécurité moderne */
    .security-score {
        position: absolute;
        top: 1.5rem;
        right: 1.5rem;
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.1rem;
        color: white;
        background: var(--gradient-info);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        transition: var(--transition);
    }

    .security-score:hover {
        transform: scale(1.1) rotate(5deg);
    }

    /* Actions de rôle avec animations */
    .role-actions {
        padding: 1rem 2rem 2rem 2rem;
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .role-actions .btn {
        border-radius: 25px;
        font-weight: 600;
        padding: 0.6rem 1.2rem;
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
    }

    .role-actions .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
    }

    .role-actions .btn:hover::before {
        left: 100%;
    }

    .role-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .btn-primary { background: var(--gradient-primary) !important; }
    .btn-info { background: var(--gradient-info) !important; }
    .btn-danger { background: var(--gradient-danger) !important; }
    .btn-outline-primary {
        border: 2px solid #667eea !important;
        color: #667eea !important;
        background: transparent !important;
    }
    .btn-outline-primary:hover {
        background: var(--gradient-primary) !important;
        color: white !important;
    }

    /* Graphiques modernes */
    .chart-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-soft);
        transition: var(--transition);
        border: none;
        overflow: hidden;
    }

    .chart-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-hover);
    }

    .chart-header {
        background: var(--gradient-primary);
        color: white;
        padding: 1.5rem;
        font-weight: 600;
        font-size: 1.1rem;
    }

    .chart-body {
        padding: 2rem;
    }

    /* Animations d'entrée */
    .fade-in-up {
        animation: fadeInUp 0.8s ease-out;
    }

    .fade-in-up:nth-child(1) { animation-delay: 0.1s; }
    .fade-in-up:nth-child(2) { animation-delay: 0.2s; }
    .fade-in-up:nth-child(3) { animation-delay: 0.3s; }
    .fade-in-up:nth-child(4) { animation-delay: 0.4s; }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .page-header {
            padding: 1.5rem;
            text-align: center;
        }

        .page-header h1 {
            font-size: 2rem;
        }

        .stats-card {
            padding: 1.5rem;
            text-align: center;
        }

        .role-card {
            margin-bottom: 1.5rem;
        }

        .security-score {
            position: static;
            margin: 1rem auto;
        }
    }

    /* Effets de survol globaux */
    .hover-lift {
        transition: var(--transition);
    }

    .hover-lift:hover {
        transform: translateY(-5px);
    }

    /* Bouton Nouveau Rôle personnalisé */
    .btn-create-role {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        font-weight: 700;
        padding: 12px 24px;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        position: relative;
        overflow: hidden;
        z-index: 1000;
    }

    .btn-create-role::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        transition: left 0.4s ease;
        z-index: -1;
    }

    .btn-create-role:hover {
        color: white;
        text-decoration: none;
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
    }

    .btn-create-role:hover::before {
        left: 0;
    }

    .btn-create-role:active {
        transform: translateY(-1px);
    }

    .btn-create-role i {
        font-size: 1.1em;
        margin-right: 8px;
    }

    /* Barre d'outils avancée */
    .toolbar-section {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-soft);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .search-filters .input-group {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .search-filters .form-control,
    .search-filters .form-select {
        border: 1px solid #e2e8f0;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
    }

    .search-filters .form-control:focus,
    .search-filters .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .view-controls .btn-group .btn {
        padding: 0.5rem 1rem;
        font-weight: 600;
        border-radius: 0;
    }

    .view-controls .btn-group .btn:first-child {
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px;
    }

    .view-controls .btn-group .btn:last-child {
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
    }

    .view-controls .btn-group .btn.active {
        background: var(--gradient-primary);
        border-color: #667eea;
        color: white;
    }

    /* Vues alternatives */
    .table-view {
        display: none;
        background: white;
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow-soft);
    }

    .table-view.active {
        display: block;
    }

    .matrix-view {
        display: none;
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-soft);
    }

    .matrix-view.active {
        display: block;
    }

    .permission-matrix {
        overflow-x: auto;
    }

    .permission-matrix table {
        min-width: 800px;
    }

    .permission-matrix th,
    .permission-matrix td {
        text-align: center;
        vertical-align: middle;
        padding: 0.75rem 0.5rem;
        border: 1px solid #e2e8f0;
    }

    .permission-matrix .permission-cell {
        width: 40px;
    }

    .permission-matrix .permission-granted {
        background: #d4edda;
        color: #155724;
    }

    .permission-matrix .permission-denied {
        background: #f8d7da;
        color: #721c24;
    }

    /* Statistiques avancées */
    .stats-advanced {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card-advanced {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: var(--shadow-soft);
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: var(--transition);
    }

    .stat-card-advanced:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow-hover);
    }

    .stat-icon-advanced {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.5rem;
        color: white;
    }

    .stat-value-advanced {
        font-size: 2rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }

    .stat-label-advanced {
        font-size: 0.9rem;
        color: #718096;
        font-weight: 600;
    }

    /* Styles pour les modales */
    .recommendation-item {
        padding: 1.5rem;
        border: 1px solid #e2e8f0;
        border-radius: 15px;
        margin-bottom: 1rem;
        transition: var(--transition);
    }

    .recommendation-item:hover {
        box-shadow: var(--shadow-soft);
        transform: translateY(-2px);
    }

    .recommendation-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .role-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 0.9rem;
    }

    /* Styles pour la vue tableau */
    .table-view .table th {
        background: var(--gradient-primary);
        color: white;
        border: none;
        font-weight: 600;
        padding: 1rem 0.75rem;
    }

    .table-view .table td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
        border-color: #e2e8f0;
    }

    .table-view .table tbody tr:hover {
        background: rgba(102, 126, 234, 0.05);
    }

    /* Styles pour la matrice des permissions */
    .matrix-view h4 {
        color: #2d3748;
        font-weight: 700;
    }

    .permission-matrix .table {
        font-size: 0.85rem;
    }

    .permission-matrix .table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #2d3748;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .permission-matrix .table th:first-child {
        position: sticky;
        left: 0;
        background: #2d3748;
        color: white;
        z-index: 11;
    }

    .permission-matrix .table td:first-child {
        position: sticky;
        left: 0;
        background: white;
        font-weight: 600;
        z-index: 9;
    }

    /* Animations pour les filtres */
    .search-filters .form-control:focus,
    .search-filters .form-select:focus {
        transform: scale(1.02);
    }

    /* Responsive pour les nouvelles fonctionnalités */
    @media (max-width: 768px) {
        .toolbar-section .row {
            flex-direction: column;
        }

        .view-controls {
            justify-content: center !important;
            margin-top: 1rem;
        }

        .search-filters .input-group {
            flex-direction: column;
        }

        .search-filters .input-group > * {
            border-radius: 10px !important;
            margin-bottom: 0.5rem;
        }

        .stats-advanced {
            grid-template-columns: repeat(2, 1fr);
        }

        .permission-matrix {
            font-size: 0.75rem;
        }

        .modal-xl {
            max-width: 95%;
        }
    }
</style>
@endpush

@section('content')
<div class="container-fluid py-4">
    <!-- Header moderne avec gradient -->
    <div class="page-header fade-in-up">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1>
                    <i class="fas fa-user-shield me-3"></i>
                    Gestion des Rôles & Permissions
                </h1>
                <p>Système de gestion avancé des rôles et permissions avec outils d'analyse</p>
            </div>
            <div class="d-flex gap-2 flex-wrap">
                <button type="button" class="btn btn-outline-light hover-lift" onclick="openBulkManager()">
                    <i class="fas fa-tasks me-2"></i>
                    Gestion en Lot
                </button>
                <button type="button" class="btn btn-outline-light hover-lift" onclick="openPermissionMatrix()">
                    <i class="fas fa-table me-2"></i>
                    Matrice
                </button>
                <button type="button" class="btn btn-outline-light hover-lift" data-bs-toggle="modal" data-bs-target="#analysisModal">
                    <i class="fas fa-chart-line me-2"></i>
                    Analyse
                </button>
                <a href="{{ route('admin.roles.create') }}"
                   class="btn btn-create-role hover-lift">
                    <i class="fas fa-plus me-2"></i>
                    Nouveau Rôle
                </a>
            </div>
        </div>
    </div>

    <!-- Barre d'outils avancée -->
    <div class="toolbar-section fade-in-up">
        <div class="row g-3">
            <div class="col-lg-8">
                <div class="search-filters">
                    <div class="input-group">
                        <span class="input-group-text bg-white border-end-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control border-start-0" id="roleSearch" placeholder="Rechercher un rôle par nom...">
                        <select class="form-select" id="activityFilter" style="max-width: 200px;">
                            <option value="">Tous les niveaux</option>
                            <option value="very_active">Très Actif</option>
                            <option value="active">Actif</option>
                            <option value="moderately_active">Modéré</option>
                            <option value="rarely_used">Rare</option>
                            <option value="inactive">Inactif</option>
                        </select>
                        <select class="form-select" id="securityFilter" style="max-width: 180px;">
                            <option value="">Tous les scores</option>
                            <option value="high">Élevé (80-100)</option>
                            <option value="medium">Moyen (60-79)</option>
                            <option value="low">Faible (0-59)</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="view-controls d-flex justify-content-end">
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-outline-primary active" id="cardViewBtn" onclick="switchView('card')">
                            <i class="fas fa-th-large me-1"></i>
                            Cartes
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="tableViewBtn" onclick="switchView('table')">
                            <i class="fas fa-table me-1"></i>
                            Tableau
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="matrixViewBtn" onclick="switchView('matrix')">
                            <i class="fas fa-th me-1"></i>
                            Matrice
                        </button>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-1"></i>
                            Exporter
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="exportRoles('pdf')"><i class="fas fa-file-pdf me-2"></i>PDF</a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportRoles('excel')"><i class="fas fa-file-excel me-2"></i>Excel</a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportRoles('csv')"><i class="fas fa-file-csv me-2"></i>CSV</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages d'alerte stylés -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show shadow-sm" role="alert" style="border-radius: 15px; border: none; background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);">
            <i class="fas fa-check-circle me-2"></i>
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show shadow-sm" role="alert" style="border-radius: 15px; border: none; background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Statistiques avancées -->
    <div class="stats-advanced fade-in-up">
        <div class="stat-card-advanced">
            <div class="stat-icon-advanced" style="background: var(--gradient-primary);">
                <i class="fas fa-user-shield"></i>
            </div>
            <div class="stat-value-advanced">{{ $roles->count() }}</div>
            <div class="stat-label-advanced">Rôles Totaux</div>
        </div>
        <div class="stat-card-advanced">
            <div class="stat-icon-advanced" style="background: var(--gradient-success);">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-value-advanced">{{ $roles->sum('users_count') }}</div>
            <div class="stat-label-advanced">Utilisateurs Assignés</div>
        </div>
        <div class="stat-card-advanced">
            <div class="stat-icon-advanced" style="background: var(--gradient-warning);">
                <i class="fas fa-key"></i>
            </div>
            <div class="stat-value-advanced">{{ $permissionStats->count() }}</div>
            <div class="stat-label-advanced">Groupes de Permissions</div>
        </div>
        <div class="stat-card-advanced">
            <div class="stat-icon-advanced" style="background: var(--gradient-info);">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="stat-value-advanced">{{ number_format($roles->avg('security_score') ?? 85, 0) }}%</div>
            <div class="stat-label-advanced">Score Sécurité Moyen</div>
        </div>
        <div class="stat-card-advanced">
            <div class="stat-icon-advanced" style="background: var(--gradient-dark);">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-value-advanced">{{ $roles->where('activity_level', 'active')->count() }}</div>
            <div class="stat-label-advanced">Rôles Actifs</div>
        </div>
    </div>

    <!-- Statistiques modernes -->
    <div class="row mb-5">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card fade-in-up">
                <div class="stats-icon primary">
                    <i class="fas fa-users-cog"></i>
                </div>
                <div class="stats-number">{{ $roleStats['total_roles'] ?? 0 }}</div>
                <div class="stats-label">Total des Rôles</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card fade-in-up">
                <div class="stats-icon success">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stats-number">{{ $roleStats['total_users'] ?? 0 }}</div>
                <div class="stats-label">Utilisateurs Assignés</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card fade-in-up">
                <div class="stats-icon warning">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="stats-number">{{ isset($roleStats['most_used']) ? $roleStats['most_used']->users_count : 0 }}</div>
                <div class="stats-label">Rôle le Plus Utilisé</div>
                @if(isset($roleStats['most_used']))
                    <small class="text-muted d-block mt-1">{{ $roleStats['most_used']->name }}</small>
                @endif
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card fade-in-up">
                <div class="stats-icon info">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="stats-number">{{ isset($roleStats['least_used']) ? $roleStats['least_used']->users_count : 0 }}</div>
                <div class="stats-label">Rôle le Moins Utilisé</div>
                @if(isset($roleStats['least_used']))
                    <small class="text-muted d-block mt-1">{{ $roleStats['least_used']->name }}</small>
                @endif
            </div>
        </div>
    </div>

    <!-- Graphiques modernes -->
    <div class="row mb-5">
        <div class="col-lg-6 mb-4">
            <div class="chart-card fade-in-up">
                <div class="chart-header">
                    <i class="fas fa-chart-pie me-2"></i>
                    Distribution des Permissions
                </div>
                <div class="chart-body">
                    <div id="permissionDistributionChart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="chart-card fade-in-up">
                <div class="chart-header">
                    <i class="fas fa-chart-radar me-2"></i>
                    Activité des Rôles
                </div>
                <div class="chart-body">
                    <div id="roleActivityChart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des rôles ultra-moderne -->
    <div class="row">
        @forelse($roles ?? [] as $index => $role)
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="role-card fade-in-up" style="animation-delay: {{ ($index * 0.1) + 0.5 }}s;">
                    <!-- Score de sécurité -->
                    <div class="security-score" title="Score de sécurité: {{ $role->security_score ?? 85 }}/100">
                        {{ $role->security_score ?? 85 }}
                    </div>

                    <!-- Header du rôle -->
                    <div class="role-header">
                        <div class="role-title">
                            <span class="activity-indicator activity-{{ $role->activity_level ?? 'active' }}"></span>
                            {{ $role->name }}
                        </div>
                        <div class="role-subtitle">
                            <i class="fas fa-users me-1"></i>
                            {{ $role->users_count ?? 0 }} utilisateur(s) assigné(s)
                        </div>
                    </div>

                    <!-- Corps du rôle -->
                    <div class="role-body">
                        <div class="role-description">
                            {{ $role->description ?? 'Aucune description disponible.' }}
                        </div>

                        <!-- Aperçu des permissions avec couleurs variées -->
                        <div class="permission-preview mb-3">
                            @php
                                $badgeColors = ['bg-primary', 'bg-success', 'bg-warning', 'bg-info'];
                                $permissions = $role->permissions ?? collect();
                            @endphp
                            @foreach($permissions->take(4) as $index => $permission)
                                <span class="permission-badge {{ $badgeColors[$index % 4] }}">
                                    {{ $permission->name }}
                                </span>
                            @endforeach
                            @if($permissions->count() > 4)
                                <span class="permission-badge bg-secondary">
                                    <i class="fas fa-plus me-1"></i>
                                    {{ $permissions->count() - 4 }} autres
                                </span>
                            @endif
                        </div>
                    </div>

                    <!-- Actions du rôle -->
                    <div class="role-actions">
                        <a href="{{ route('admin.roles.edit', $role) }}"
                           class="btn btn-primary btn-sm">
                            <i class="fas fa-edit me-1"></i>
                            Modifier
                        </a>
                        <button type="button"
                                class="btn btn-info btn-sm"
                                onclick="analyzeRole({{ $role->id }})">
                            <i class="fas fa-analytics me-1"></i>
                            Analyser
                        </button>
                        @if($role->name !== 'admin')
                            <form action="{{ route('admin.roles.destroy', $role) }}"
                                  method="POST"
                                  class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="button"
                                        class="btn btn-danger btn-sm"
                                        onclick="deleteRoleWithForm({{ $role->id }}, '{{ $role->name }}')">
                                    <i class="fas fa-trash me-1"></i>
                                    Supprimer
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-user-shield fa-4x text-muted"></i>
                    </div>
                    <h4 class="text-muted">Aucun rôle trouvé</h4>
                    <p class="text-muted">Commencez par créer votre premier rôle.</p>
                    <a href="{{ route('admin.roles.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Créer un Rôle
                    </a>
                </div>
            </div>
        @endforelse
    </div>

    <!-- Vue Tableau -->
    <div class="table-view" id="tableViewContainer">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th><input type="checkbox" id="selectAllRoles" class="form-check-input"></th>
                        <th>Rôle</th>
                        <th>Utilisateurs</th>
                        <th>Permissions</th>
                        <th>Score Sécurité</th>
                        <th>Activité</th>
                        <th>Dernière Utilisation</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($roles ?? [] as $role)
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input role-checkbox" value="{{ $role->id }}">
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="role-avatar me-2" style="background: {{ $role->color ?? '#667eea' }};">
                                    {{ strtoupper(substr($role->name, 0, 2)) }}
                                </div>
                                <div>
                                    <strong>{{ $role->name }}</strong>
                                    @if($role->description)
                                        <br><small class="text-muted">{{ Str::limit($role->description, 50) }}</small>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-primary">{{ $role->users_count ?? 0 }}</span>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ $role->permissions->count() }}</span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="progress me-2" style="width: 60px; height: 8px;">
                                    <div class="progress-bar bg-{{ $role->security_score >= 80 ? 'success' : ($role->security_score >= 60 ? 'warning' : 'danger') }}"
                                         style="width: {{ $role->security_score ?? 85 }}%"></div>
                                </div>
                                <small>{{ $role->security_score ?? 85 }}%</small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-{{ $role->activity_level === 'active' ? 'success' : ($role->activity_level === 'moderately_active' ? 'warning' : 'secondary') }}">
                                {{ ucfirst($role->activity_level ?? 'active') }}
                            </span>
                        </td>
                        <td>
                            <small class="text-muted">
                                {{ $role->last_used ? \Carbon\Carbon::parse($role->last_used)->diffForHumans() : 'Jamais' }}
                            </small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ route('admin.roles.edit', $role) }}" class="btn btn-outline-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-outline-info" onclick="analyzeRole({{ $role->id }})">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                                @if($role->name !== 'admin')
                                <button type="button" class="btn btn-outline-danger" onclick="deleteRole({{ $role->id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Vue Matrice des Permissions -->
    <div class="matrix-view" id="matrixViewContainer">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4><i class="fas fa-th me-2"></i>Matrice des Permissions</h4>
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary" onclick="expandMatrix()">
                    <i class="fas fa-expand me-1"></i>Étendre
                </button>
                <button class="btn btn-outline-secondary" onclick="exportMatrix()">
                    <i class="fas fa-download me-1"></i>Exporter
                </button>
            </div>
        </div>

        <div class="permission-matrix">
            <table class="table table-bordered table-sm">
                <thead>
                    <tr>
                        <th style="min-width: 150px;">Rôles / Permissions</th>
                        @foreach($permissionStats ?? [] as $group => $permissions)
                            <th colspan="{{ $permissions->count() }}" class="text-center bg-light">
                                {{ ucfirst($group) }}
                            </th>
                        @endforeach
                    </tr>
                    <tr>
                        <th></th>
                        @foreach($permissionStats ?? [] as $group => $permissions)
                            @foreach($permissions as $permission)
                                <th class="permission-cell" title="{{ $permission->name }}">
                                    {{ Str::limit(str_replace($group.'.', '', $permission->name), 8) }}
                                </th>
                            @endforeach
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    @foreach($roles ?? [] as $role)
                    <tr>
                        <td class="fw-bold">{{ $role->name }}</td>
                        @foreach($permissionStats ?? [] as $group => $permissions)
                            @foreach($permissions as $permission)
                                <td class="permission-cell {{ $role->permissions->contains('id', $permission->id) ? 'permission-granted' : 'permission-denied' }}">
                                    @if($role->permissions->contains('id', $permission->id))
                                        <i class="fas fa-check"></i>
                                    @else
                                        <i class="fas fa-times"></i>
                                    @endif
                                </td>
                            @endforeach
                        @endforeach
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal d'analyse moderne -->
<div class="modal fade" id="analysisModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content" style="border-radius: 20px; border: none; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);">
            <div class="modal-header" style="background: var(--gradient-primary); color: white; border-radius: 20px 20px 0 0; border: none;">
                <h5 class="modal-title">
                    <i class="fas fa-chart-line me-2"></i>
                    Analyse Avancée des Rôles
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" style="padding: 2rem;">
                <div class="row">
                    <div class="col-lg-6 mb-4">
                        <div class="chart-card">
                            <div class="chart-header">
                                <i class="fas fa-layer-group me-2"></i>
                                Distribution par Groupe
                            </div>
                            <div class="chart-body">
                                <div id="permissionGroupChart" style="height: 300px;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 mb-4">
                        <div class="chart-card">
                            <div class="chart-header">
                                <i class="fas fa-activity me-2"></i>
                                Niveaux d'Activité
                            </div>
                            <div class="chart-body">
                                <div id="activityLevelChart" style="height: 300px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="chart-card">
                            <div class="chart-header">
                                <i class="fas fa-lightbulb me-2"></i>
                                Recommandations Intelligentes
                            </div>
                            <div class="chart-body">
                                <div id="recommendations">
                                    <div class="d-flex justify-content-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Chargement...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer moderne -->
<footer class="mt-5 py-4" style="background: var(--gradient-dark); color: white;">
    <div class="container text-center">
        <div class="row">
            <div class="col-12">
                <p class="mb-2">
                    <i class="fas fa-copyright me-1"></i>
                    {{ date('Y') }} GRADIS - Système de Gestion Moderne
                </p>
                <p class="mb-0 text-muted">
                    <i class="fas fa-code me-1"></i>
                    Développé avec ❤️ par MOMK-Solutions
                </p>
            </div>
        </div>
    </div>
</footer>

<!-- Modal de Gestion en Lot -->
<div class="modal fade" id="bulkManagerModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-tasks me-2"></i>
                    Gestion en Lot des Rôles
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="bulk-actions">
                    <h6><i class="fas fa-cogs me-2"></i>Actions Disponibles</h6>
                    <button class="btn btn-success" onclick="bulkAction('activate')">
                        <i class="fas fa-check me-1"></i>Activer Sélectionnés
                    </button>
                    <button class="btn btn-warning" onclick="bulkAction('deactivate')">
                        <i class="fas fa-pause me-1"></i>Désactiver Sélectionnés
                    </button>
                    <button class="btn btn-info" onclick="bulkAction('analyze')">
                        <i class="fas fa-chart-line me-1"></i>Analyser Sélectionnés
                    </button>
                    <button class="btn btn-danger" onclick="bulkAction('delete')">
                        <i class="fas fa-trash me-1"></i>Supprimer Sélectionnés
                    </button>
                </div>

                <div class="bulk-manager">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="selectAllBulk" class="form-check-input"></th>
                                    <th>Rôle</th>
                                    <th>Utilisateurs</th>
                                    <th>Permissions</th>
                                    <th>Score</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($roles ?? [] as $role)
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input bulk-role-checkbox"
                                               value="{{ $role->id }}" {{ $role->name === 'admin' ? 'disabled' : '' }}>
                                    </td>
                                    <td>
                                        <strong>{{ $role->name }}</strong>
                                        @if($role->name === 'admin')
                                            <span class="badge bg-warning ms-2">Protégé</span>
                                        @endif
                                    </td>
                                    <td>{{ $role->users_count ?? 0 }}</td>
                                    <td>{{ $role->permissions->count() }}</td>
                                    <td>
                                        <span class="badge bg-{{ $role->security_score >= 80 ? 'success' : ($role->security_score >= 60 ? 'warning' : 'danger') }}">
                                            {{ $role->security_score ?? 85 }}%
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $role->activity_level === 'active' ? 'success' : 'secondary' }}">
                                            {{ ucfirst($role->activity_level ?? 'active') }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" onclick="executeBulkActions()">
                    <i class="fas fa-play me-1"></i>Exécuter les Actions
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'Analyse Avancée des Rôles -->
<div class="modal fade" id="roleAnalysisModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="fas fa-chart-line me-2"></i>
                    Analyse Détaillée du Rôle
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="roleAnalysisContent">
                <!-- Contenu dynamique généré par JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" onclick="exportAnalysis()">
                    <i class="fas fa-download me-1"></i>Exporter l'Analyse
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Recommandations -->
<div class="modal fade" id="recommendationsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-lightbulb me-2"></i>
                    Recommandations Intelligentes
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Analyse IA :</strong> Voici nos recommandations pour optimiser votre système de rôles et permissions.
                </div>

                <div class="recommendations-list">
                    <div class="recommendation-item">
                        <div class="d-flex align-items-start">
                            <div class="recommendation-icon bg-warning text-white me-3">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div>
                                <h6>Rôles sous-utilisés détectés</h6>
                                <p class="text-muted">Certains rôles n'ont pas été utilisés récemment. Considérez leur suppression ou fusion.</p>
                                <button class="btn btn-sm btn-outline-warning">Voir les détails</button>
                            </div>
                        </div>
                    </div>

                    <div class="recommendation-item">
                        <div class="d-flex align-items-start">
                            <div class="recommendation-icon bg-info text-white me-3">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div>
                                <h6>Amélioration de la sécurité</h6>
                                <p class="text-muted">Certains rôles ont trop de permissions. Appliquez le principe du moindre privilège.</p>
                                <button class="btn btn-sm btn-outline-info">Optimiser</button>
                            </div>
                        </div>
                    </div>

                    <div class="recommendation-item">
                        <div class="d-flex align-items-start">
                            <div class="recommendation-icon bg-success text-white me-3">
                                <i class="fas fa-users"></i>
                            </div>
                            <div>
                                <h6>Répartition optimale</h6>
                                <p class="text-muted">La répartition des utilisateurs par rôle est équilibrée. Bon travail !</p>
                                <button class="btn btn-sm btn-outline-success">Maintenir</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-success">
                    <i class="fas fa-magic me-1"></i>Appliquer les Recommandations
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/apexcharts@3.41.0/dist/apexcharts.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configuration moderne des graphiques avec thème sombre
    const chartTheme = {
        theme: {
            mode: 'light',
            palette: 'palette1'
        },
        colors: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#36d1dc', '#5b86e5'],
        chart: {
            fontFamily: 'Inter, sans-serif',
            toolbar: {
                show: true,
                tools: {
                    download: true,
                    selection: false,
                    zoom: false,
                    zoomin: false,
                    zoomout: false,
                    pan: false,
                    reset: false
                }
            }
        },
        dataLabels: {
            style: {
                fontSize: '12px',
                fontWeight: 600
            }
        }
    };

    // Graphique de distribution des permissions (moderne)
    @if(isset($roleStats['permission_distribution']))
    const permissionDistribution = new ApexCharts(document.querySelector("#permissionDistributionChart"), {
        ...chartTheme,
        series: @json($roleStats['permission_distribution']->pluck('permissions_count') ?? []),
        labels: @json($roleStats['permission_distribution']->pluck('name') ?? []),
        chart: {
            ...chartTheme.chart,
            type: 'donut',
            height: 350,
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        plotOptions: {
            pie: {
                donut: {
                    size: '65%',
                    labels: {
                        show: true,
                        total: {
                            show: true,
                            label: 'Total',
                            fontSize: '16px',
                            fontWeight: 600
                        }
                    }
                }
            }
        },
        legend: {
            position: 'bottom',
            fontSize: '14px',
            fontWeight: 500
        },
        tooltip: {
            style: {
                fontSize: '14px'
            }
        }
    });
    permissionDistribution.render();
    @endif

    // Graphique d'activité des rôles (radar moderne)
    @if(isset($roles))
    const roleActivityData = @json($roles->groupBy('activity_level')->map->count() ?? []);
    const activityChart = new ApexCharts(document.querySelector("#roleActivityChart"), {
        ...chartTheme,
        series: [{
            name: 'Nombre de rôles',
            data: Object.values(roleActivityData)
        }],
        labels: Object.keys(roleActivityData).map(key => {
            const translations = {
                'very_active': 'Très Actif',
                'active': 'Actif',
                'moderately_active': 'Modérément Actif',
                'rarely_used': 'Rarement Utilisé',
                'inactive': 'Inactif'
            };
            return translations[key] || key.replace('_', ' ').toUpperCase();
        }),
        chart: {
            ...chartTheme.chart,
            type: 'radar',
            height: 350
        },
        plotOptions: {
            radar: {
                size: 140,
                polygons: {
                    strokeColors: '#e9e9e9',
                    fill: {
                        colors: ['#f8f9fa', '#ffffff']
                    }
                }
            }
        },
        markers: {
            size: 4,
            colors: ['#667eea'],
            strokeColor: '#fff',
            strokeWidth: 2
        },
        tooltip: {
            y: {
                formatter: function(val) {
                    return val + ' rôle(s)'
                }
            }
        }
    });
    activityChart.render();
    @endif

    // Fonction d'analyse de rôle améliorée
    window.analyzeRole = function(roleId) {
        // Afficher un loader moderne
        Swal.fire({
            title: 'Analyse en cours...',
            html: '<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"></div></div>',
            showConfirmButton: false,
            allowOutsideClick: false
        });

        // Simuler une analyse (remplacer par un vrai appel API)
        setTimeout(() => {
            const mockData = {
                security_score: Math.floor(Math.random() * 30) + 70,
                permission_conflicts: Math.floor(Math.random() * 3),
                unused_permissions: Math.floor(Math.random() * 5),
                suggested_optimizations: [
                    'Réviser les permissions d\'écriture',
                    'Optimiser les accès administrateur',
                    'Vérifier les permissions obsolètes'
                ]
            };

            Swal.fire({
                title: '<i class="fas fa-chart-line me-2"></i>Analyse du Rôle',
                html: `
                    <div class="text-start">
                        <div class="row mb-3">
                            <div class="col-4 text-center">
                                <div class="h3 text-primary">${mockData.security_score}/100</div>
                                <small class="text-muted">Score de Sécurité</small>
                            </div>
                            <div class="col-4 text-center">
                                <div class="h3 text-warning">${mockData.permission_conflicts}</div>
                                <small class="text-muted">Conflits</small>
                            </div>
                            <div class="col-4 text-center">
                                <div class="h3 text-info">${mockData.unused_permissions}</div>
                                <small class="text-muted">Non Utilisées</small>
                            </div>
                        </div>
                        <hr>
                        <h6 class="mt-3 mb-2"><i class="fas fa-lightbulb me-2 text-warning"></i>Recommandations:</h6>
                        <ul class="list-unstyled">
                            ${mockData.suggested_optimizations.map(opt => `
                                <li class="mb-2">
                                    <i class="fas fa-check-circle me-2 text-success"></i>
                                    ${opt}
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `,
                icon: 'info',
                width: '600px',
                confirmButtonText: '<i class="fas fa-check me-2"></i>Compris',
                customClass: {
                    confirmButton: 'btn btn-primary'
                }
            });
        }, 1500);
    };

    // Animation d'entrée pour les cartes
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observer toutes les cartes
    document.querySelectorAll('.fade-in-up').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s ease-out';
        observer.observe(card);
    });

    // Fonctions pour les nouvelles fonctionnalités
    window.switchView = function(viewType) {
        // Masquer toutes les vues
        document.querySelectorAll('.row, .table-view, .matrix-view').forEach(view => {
            if (view.classList.contains('row') && view.querySelector('.role-card')) {
                view.style.display = viewType === 'card' ? 'flex' : 'none';
            } else if (view.classList.contains('table-view')) {
                view.style.display = viewType === 'table' ? 'block' : 'none';
            } else if (view.classList.contains('matrix-view')) {
                view.style.display = viewType === 'matrix' ? 'block' : 'none';
            }
        });

        // Mettre à jour les boutons
        document.querySelectorAll('.view-controls .btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.getElementById(viewType + 'ViewBtn').classList.add('active');
    };

    window.openBulkManager = function() {
        const modal = new bootstrap.Modal(document.getElementById('bulkManagerModal'));
        modal.show();
    };

    window.openPermissionMatrix = function() {
        switchView('matrix');
    };

    window.exportRoles = function(format) {
        const roles = @json($roles ?? []);
        let content = '';

        if (format === 'csv') {
            content = 'Nom,Utilisateurs,Permissions,Score Sécurité,Activité\n';
            roles.forEach(role => {
                content += `"${role.name}",${role.users_count || 0},${role.permissions ? role.permissions.length : 0},${role.security_score || 85},"${role.activity_level || 'active'}"\n`;
            });
            downloadFile(content, 'roles.csv', 'text/csv');
        } else if (format === 'json') {
            content = JSON.stringify(roles, null, 2);
            downloadFile(content, 'roles.json', 'application/json');
        }
    };

    function downloadFile(content, filename, contentType) {
        const blob = new Blob([content], { type: contentType });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    // Filtres et recherche
    document.getElementById('roleSearch').addEventListener('input', function() {
        filterRoles();
    });

    document.getElementById('activityFilter').addEventListener('change', function() {
        filterRoles();
    });

    document.getElementById('securityFilter').addEventListener('change', function() {
        filterRoles();
    });

    function filterRoles() {
        const searchTerm = document.getElementById('roleSearch').value.toLowerCase();
        const activityFilter = document.getElementById('activityFilter').value;
        const securityFilter = document.getElementById('securityFilter').value;

        document.querySelectorAll('.role-card').forEach(card => {
            const roleName = card.querySelector('.role-title').textContent.toLowerCase();
            const roleActivity = card.dataset.activity || 'active';
            const roleScore = parseInt(card.querySelector('.security-score').textContent) || 85;

            let showCard = true;

            // Filtre de recherche
            if (searchTerm && !roleName.includes(searchTerm)) {
                showCard = false;
            }

            // Filtre d'activité
            if (activityFilter && roleActivity !== activityFilter) {
                showCard = false;
            }

            // Filtre de sécurité
            if (securityFilter) {
                if (securityFilter === 'high' && roleScore < 80) showCard = false;
                if (securityFilter === 'medium' && (roleScore < 60 || roleScore >= 80)) showCard = false;
                if (securityFilter === 'low' && roleScore >= 60) showCard = false;
            }

            card.closest('.col-lg-6').style.display = showCard ? 'block' : 'none';
        });
    }

    // Gestion des sélections multiples
    document.getElementById('selectAllRoles').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.role-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Fonctions pour la gestion en lot
    window.bulkAction = function(action) {
        const selectedRoles = Array.from(document.querySelectorAll('.bulk-role-checkbox:checked')).map(cb => cb.value);

        if (selectedRoles.length === 0) {
            alert('Veuillez sélectionner au moins un rôle.');
            return;
        }

        console.log(`Action ${action} sur les rôles:`, selectedRoles);

        // Ici vous pouvez ajouter la logique pour chaque action
        switch(action) {
            case 'activate':
                alert(`Activation de ${selectedRoles.length} rôle(s)`);
                break;
            case 'deactivate':
                alert(`Désactivation de ${selectedRoles.length} rôle(s)`);
                break;
            case 'analyze':
                alert(`Analyse de ${selectedRoles.length} rôle(s)`);
                break;
            case 'delete':
                bulkDeleteRoles(selectedRoles);
                break;
        }
    };

    window.executeBulkActions = function() {
        const selectedRoles = Array.from(document.querySelectorAll('.bulk-role-checkbox:checked')).map(cb => cb.value);

        if (selectedRoles.length === 0) {
            alert('Aucun rôle sélectionné pour les actions en lot.');
            return;
        }

        // Simuler l'exécution des actions
        const modal = bootstrap.Modal.getInstance(document.getElementById('bulkManagerModal'));
        modal.hide();

        // Afficher une notification de succès
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show';
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            Actions en lot exécutées avec succès sur ${selectedRoles.length} rôle(s).
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.page-header'));

        setTimeout(() => alertDiv.remove(), 5000);
    };

    // Fonction d'analyse détaillée d'un rôle
    window.analyzeRole = function(roleId) {
        const roles = @json($roles ?? []);
        const role = roles.find(r => r.id == roleId);

        if (!role) return;

        const analysisContent = `
            <div class="row">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <h6 class="card-title"><i class="fas fa-info-circle me-2"></i>Informations Générales</h6>
                            <ul class="list-unstyled">
                                <li><strong>Nom:</strong> ${role.name}</li>
                                <li><strong>Utilisateurs assignés:</strong> ${role.users_count || 0}</li>
                                <li><strong>Permissions:</strong> ${role.permissions ? role.permissions.length : 0}</li>
                                <li><strong>Score de sécurité:</strong> ${role.security_score || 85}%</li>
                                <li><strong>Niveau d'activité:</strong> ${role.activity_level || 'active'}</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <h6 class="card-title"><i class="fas fa-chart-bar me-2"></i>Statistiques d'Usage</h6>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-primary" style="width: ${(role.users_count || 0) * 10}%"></div>
                            </div>
                            <small class="text-muted">Utilisation relative</small>

                            <div class="mt-3">
                                <div class="d-flex justify-content-between">
                                    <span>Sécurité</span>
                                    <span>${role.security_score || 85}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-${role.security_score >= 80 ? 'success' : (role.security_score >= 60 ? 'warning' : 'danger')}"
                                         style="width: ${role.security_score || 85}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-key me-2"></i>Permissions Détaillées</h6>
                        <div class="row">
                            ${role.permissions ? role.permissions.map(permission => `
                                <div class="col-md-4 mb-2">
                                    <span class="badge bg-light text-dark">${permission.name}</span>
                                </div>
                            `).join('') : '<p class="text-muted">Aucune permission assignée</p>'}
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('roleAnalysisContent').innerHTML = analysisContent;
        const modal = new bootstrap.Modal(document.getElementById('roleAnalysisModal'));
        modal.show();
    };

    window.exportAnalysis = function() {
        alert('Fonctionnalité d\'export d\'analyse en cours de développement');
    };

    window.expandMatrix = function() {
        const matrixView = document.getElementById('matrixViewContainer');
        if (matrixView.requestFullscreen) {
            matrixView.requestFullscreen();
        }
    };

    window.exportMatrix = function() {
        alert('Export de la matrice des permissions en cours de développement');
    };

    // Fonction pour la suppression en lot
    function bulkDeleteRoles(selectedRoles) {
        if (selectedRoles.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'Aucune sélection',
                text: 'Veuillez sélectionner au moins un rôle à supprimer.',
                confirmButtonColor: '#3085d6'
            });
            return;
        }

        Swal.fire({
            title: '🗑️ Suppression multiple',
            html: `
                <div style="text-align: left; margin: 1rem 0;">
                    <p style="color: #6b7280; margin-bottom: 1rem;">
                        <i class="fas fa-exclamation-triangle" style="color: #f59e0b;"></i>
                        Vous êtes sur le point de supprimer <strong style="color: #dc2626;">${selectedRoles.length} rôle(s)</strong> de manière permanente.
                    </p>
                    <div style="background: #fef3c7; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                        <h6 style="color: #92400e; margin: 0 0 0.5rem 0;">
                            <i class="fas fa-info-circle"></i> Conséquences :
                        </h6>
                        <ul style="color: #92400e; margin: 0; padding-left: 1.5rem;">
                            <li>Tous les utilisateurs avec ces rôles perdront leurs permissions</li>
                            <li>Les règles de sécurité associées seront supprimées</li>
                            <li>Cette action est <strong>irréversible</strong></li>
                        </ul>
                    </div>
                    <p style="color: #6b7280;">
                        Pour confirmer la suppression, veuillez saisir <strong style="color: #dc2626;">SUPPRIMER TOUT</strong> dans le champ ci-dessous :
                    </p>
                </div>
            `,
            input: 'text',
            inputPlaceholder: 'Tapez SUPPRIMER TOUT pour confirmer',
            icon: 'warning',
            iconColor: '#f59e0b',
            showCancelButton: true,
            confirmButtonColor: '#dc2626',
            cancelButtonColor: '#6b7280',
            confirmButtonText: `<i class="fas fa-trash me-2"></i>Supprimer ${selectedRoles.length} rôle(s)`,
            cancelButtonText: '<i class="fas fa-times me-2"></i>Annuler',
            buttonsStyling: true,
            preConfirm: (inputValue) => {
                if (inputValue !== 'SUPPRIMER TOUT') {
                    Swal.showValidationMessage('Vous devez taper exactement "SUPPRIMER TOUT" pour confirmer');
                    return false;
                }
                return true;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Afficher un loader pendant la suppression
                Swal.fire({
                    title: 'Suppression en cours...',
                    html: `<div class="spinner-border text-danger" role="status"></div><br><br>Suppression de ${selectedRoles.length} rôle(s)...`,
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Ici vous pourriez implémenter la logique de suppression en lot
                // Pour l'instant, on simule avec un délai
                setTimeout(() => {
                    Swal.fire({
                        icon: 'success',
                        title: 'Suppression réussie',
                        text: `${selectedRoles.length} rôle(s) ont été supprimé(s) avec succès.`,
                        confirmButtonColor: '#059669'
                    }).then(() => {
                        location.reload();
                    });
                }, 2000);
            }
        });
    }

    window.deleteRole = function(roleId) {
        Swal.fire({
            title: '🗑️ Supprimer le rôle',
            html: `
                <div style="text-align: left; margin: 1rem 0;">
                    <p style="color: #6b7280; margin-bottom: 1rem;">
                        <i class="fas fa-exclamation-triangle" style="color: #f59e0b;"></i>
                        Vous êtes sur le point de supprimer ce rôle de manière <strong style="color: #dc2626;">permanente</strong>.
                    </p>
                    <div style="background: #fef3c7; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                        <h6 style="color: #92400e; margin: 0 0 0.5rem 0;">
                            <i class="fas fa-info-circle"></i> Conséquences :
                        </h6>
                        <ul style="color: #92400e; margin: 0; padding-left: 1.5rem;">
                            <li>Tous les utilisateurs avec ce rôle perdront leurs permissions</li>
                            <li>Les règles de sécurité associées seront supprimées</li>
                            <li>Cette action est <strong>irréversible</strong></li>
                        </ul>
                    </div>
                    <p style="color: #6b7280;">
                        Pour confirmer la suppression, veuillez saisir <strong style="color: #dc2626;">SUPPRIMER</strong> dans le champ ci-dessous :
                    </p>
                </div>
            `,
            input: 'text',
            inputPlaceholder: 'Tapez SUPPRIMER pour confirmer',
            icon: 'warning',
            iconColor: '#f59e0b',
            showCancelButton: true,
            confirmButtonColor: '#dc2626',
            cancelButtonColor: '#6b7280',
            confirmButtonText: '<i class="fas fa-trash me-2"></i>Supprimer définitivement',
            cancelButtonText: '<i class="fas fa-times me-2"></i>Annuler',
            buttonsStyling: true,
            customClass: {
                popup: 'modern-swal-popup',
                confirmButton: 'modern-swal-confirm-danger',
                cancelButton: 'modern-swal-cancel'
            },
            preConfirm: (inputValue) => {
                if (inputValue !== 'SUPPRIMER') {
                    Swal.showValidationMessage('Vous devez taper exactement "SUPPRIMER" pour confirmer');
                    return false;
                }
                return true;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Afficher un loader pendant la suppression
                Swal.fire({
                    title: 'Suppression en cours...',
                    html: '<div class="spinner-border text-danger" role="status"></div>',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Créer et soumettre le formulaire de suppression
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/roles/${roleId}`;

                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';
                form.appendChild(csrfToken);

                const methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';
                form.appendChild(methodField);

                document.body.appendChild(form);
                form.submit();
            }
        });
    };

    // Fonction pour supprimer un rôle avec le formulaire existant
    window.deleteRoleWithForm = function(roleId, roleName) {
        Swal.fire({
            title: '🗑️ Supprimer le rôle',
            html: `
                <div style="text-align: left; margin: 1rem 0;">
                    <p style="color: #6b7280; margin-bottom: 1rem;">
                        <i class="fas fa-exclamation-triangle" style="color: #f59e0b;"></i>
                        Vous êtes sur le point de supprimer le rôle <strong style="color: #dc2626;">"${roleName}"</strong> de manière permanente.
                    </p>
                    <div style="background: #fef3c7; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                        <h6 style="color: #92400e; margin: 0 0 0.5rem 0;">
                            <i class="fas fa-info-circle"></i> Conséquences :
                        </h6>
                        <ul style="color: #92400e; margin: 0; padding-left: 1.5rem;">
                            <li>Tous les utilisateurs avec ce rôle perdront leurs permissions</li>
                            <li>Les règles de sécurité associées seront supprimées</li>
                            <li>Cette action est <strong>irréversible</strong></li>
                        </ul>
                    </div>
                    <p style="color: #6b7280;">
                        Pour confirmer la suppression, veuillez saisir <strong style="color: #dc2626;">SUPPRIMER</strong> dans le champ ci-dessous :
                    </p>
                </div>
            `,
            input: 'text',
            inputPlaceholder: 'Tapez SUPPRIMER pour confirmer',
            icon: 'warning',
            iconColor: '#f59e0b',
            showCancelButton: true,
            confirmButtonColor: '#dc2626',
            cancelButtonColor: '#6b7280',
            confirmButtonText: '<i class="fas fa-trash me-2"></i>Supprimer définitivement',
            cancelButtonText: '<i class="fas fa-times me-2"></i>Annuler',
            buttonsStyling: true,
            preConfirm: (inputValue) => {
                if (inputValue !== 'SUPPRIMER') {
                    Swal.showValidationMessage('Vous devez taper exactement "SUPPRIMER" pour confirmer');
                    return false;
                }
                return true;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Afficher un loader pendant la suppression
                Swal.fire({
                    title: 'Suppression en cours...',
                    html: '<div class="spinner-border text-danger" role="status"></div>',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Soumettre le formulaire existant
                const form = document.querySelector(`form[action*="/admin/roles/${roleId}"]`);
                if (form) {
                    form.submit();
                } else {
                    // Fallback: créer un nouveau formulaire
                    const newForm = document.createElement('form');
                    newForm.method = 'POST';
                    newForm.action = `/admin/roles/${roleId}`;

                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = '_token';
                    csrfToken.value = '{{ csrf_token() }}';
                    newForm.appendChild(csrfToken);

                    const methodField = document.createElement('input');
                    methodField.type = 'hidden';
                    methodField.name = '_method';
                    methodField.value = 'DELETE';
                    newForm.appendChild(methodField);

                    document.body.appendChild(newForm);
                    newForm.submit();
                }
            }
        });
    };

});
</script>
@endpush
