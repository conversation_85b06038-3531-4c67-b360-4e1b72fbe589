@extends('layouts.admin_minimal')

@section('title', 'Modifier un rôle')

@push('styles')
<style>
    /* Variables CSS personnalisées */
    :root {
        --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --gradient-info: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
        --gradient-dark: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.1);
        --shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
        --border-radius: 20px;
        --transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    }

    /* Suppression du background body pour éviter les conflits avec le layout */
    .main-content {
        font-family: 'Inter', sans-serif;
        min-height: 100vh;
    }

    /* Protection renforcée contre la disparition de la page */
    .container-fluid,
    .main-content,
    .form-container,
    .page-header,
    .role-edit-form {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        z-index: 1 !important;
    }

    /* Protection spécifique contre les interférences d'extensions */
    body .main-content,
    html .main-content,
    .main-content .container-fluid {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        background: transparent !important;
    }

    /* Forcer l'affichage même si des styles externes tentent de le masquer */
    .main-content[style*="display: none"],
    .main-content[style*="visibility: hidden"],
    .main-content[style*="opacity: 0"] {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Protection contre les transformations qui pourraient masquer le contenu */
    .main-content,
    .container-fluid {
        transform: none !important;
        transition: none !important;
    }

    /* Correction essentielle pour l'affichage du contenu */
    .form-container,
    .form-section,
    .main-card,
    .container-fluid {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* CORRECTION CRITIQUE - Désactiver l'animation fade-in-up qui masque le contenu */
    .fade-in-up {
        opacity: 1 !important;
        transform: translateY(0) !important;
        animation: none !important;
    }

    /* Forcer la visibilité de tous les éléments animés */
    .fade-in-up,
    .fade-in-up * {
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
    }

    /* Header moderne avec gradient */
    .page-header {
        background: var(--gradient-primary);
        border-radius: var(--border-radius);
        padding: 2.5rem;
        margin-bottom: 2rem;
        color: white;
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-soft);
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .page-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 2;
    }

    .page-header .subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-top: 0.5rem;
        position: relative;
        z-index: 2;
    }

    .back-button {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 15px;
        text-decoration: none;
        font-weight: 600;
        transition: var(--transition);
        backdrop-filter: blur(10px);
        position: relative;
        z-index: 2;
    }

    .back-button:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }

    /* Carte principale ultra-moderne */
    .main-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-soft);
        border: none;
        overflow: hidden;
        transition: var(--transition);
        position: relative;
    }

    .main-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 6px;
        background: var(--gradient-primary);
    }

    .main-card:hover {
        box-shadow: var(--shadow-hover);
        transform: translateY(-5px);
    }

    .card-header-custom {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 2rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .card-header-custom h2 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: 700;
        color: #2d3748;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .role-icon {
        width: 60px;
        height: 60px;
        background: var(--gradient-info);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    /* Formulaire moderne */
    .form-section {
        padding: 2.5rem;
    }

    .form-group-modern {
        margin-bottom: 2rem;
        position: relative;
    }

    .form-label-modern {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.75rem;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-control-modern {
        border: 2px solid #e2e8f0;
        border-radius: 15px;
        padding: 1rem 1.5rem;
        font-size: 1rem;
        transition: var(--transition);
        background: #f8f9fa;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .form-control-modern:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        background: white;
        outline: none;
    }

    .form-control-modern:read-only {
        background: #f1f3f4;
        color: #6c757d;
        cursor: not-allowed;
    }

    /* Section des permissions ultra-moderne */
    .permissions-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 20px;
        padding: 2rem;
        margin-top: 1.5rem;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .permissions-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #e2e8f0;
    }

    .permissions-icon {
        width: 50px;
        height: 50px;
        background: var(--gradient-warning);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    }

    .permissions-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2d3748;
        margin: 0;
    }

    .permissions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
    }

    .permission-card {
        background: white;
        border: 3px solid #e2e8f0;
        border-radius: 15px;
        padding: 1.5rem;
        transition: var(--transition);
        position: relative;
        cursor: pointer;
        overflow: hidden;
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .permission-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        transition: left 0.5s;
    }

    .permission-card:hover::before {
        left: 100%;
    }

    .permission-card:hover {
        border-color: #667eea;
        transform: translateY(-5px) scale(1.02);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
    }

    .permission-card.checked {
        border-color: #28a745;
        background: linear-gradient(135deg, #28a74515 0%, #20c99720 100%);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    }

    .permission-card.checked::after {
        content: '✓';
        position: absolute;
        top: 10px;
        right: 15px;
        background: #28a745;
        color: white;
        width: 25px;
        height: 25px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: bold;
    }

    .permission-checkbox {
        width: 24px;
        height: 24px;
        margin-right: 1rem;
        accent-color: #667eea;
        cursor: pointer;
        transform: scale(1.2);
    }

    .permission-label {
        font-weight: 700;
        color: #2d3748;
        cursor: pointer;
        display: flex;
        align-items: center;
        margin: 0;
        font-size: 1.1rem;
        padding: 0.5rem 0;
    }

    .permission-label:hover {
        color: #667eea;
    }

    .permission-card.checked .permission-label {
        color: #28a745;
    }

    .permission-description {
        font-size: 0.9rem;
        color: #718096;
        margin-top: 0.75rem;
        line-height: 1.4;
        font-style: italic;
    }

    .permission-card.checked .permission-description {
        color: #155724;
    }

    /* Indicateur visuel pour les permissions sélectionnées */
    .permission-card.checked .permission-checkbox {
        background: #28a745;
        border-color: #28a745;
    }

    /* Animation de sélection */
    .permission-card.checked {
        animation: permissionSelected 0.3s ease-out;
    }

    @keyframes permissionSelected {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    /* Boutons d'action modernes */
    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        padding: 2rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    .btn-modern {
        padding: 1rem 2.5rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 1.1rem;
        border: none;
        cursor: pointer;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
    }

    .btn-modern:hover::before {
        left: 100%;
    }

    .btn-primary-modern {
        background: var(--gradient-primary);
        color: white;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-primary-modern:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-primary-modern:disabled {
        background: #6c757d;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .btn-secondary-modern {
        background: var(--gradient-dark);
        color: white;
        box-shadow: 0 5px 15px rgba(44, 62, 80, 0.3);
    }

    .btn-secondary-modern:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(44, 62, 80, 0.4);
        color: white;
    }

    /* Animations */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .fade-in-up {
        animation: fadeInUp 0.6s ease-out;
    }

    /* Messages d'alerte stylés */
    .alert-modern {
        border: none;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-soft);
    }

    .alert-success-modern {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
        border-left: 5px solid #28a745;
    }

    .alert-danger-modern {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
        border-left: 5px solid #dc3545;
    }

    /* Footer moderne */
    .footer-modern {
        background: var(--gradient-dark);
        color: white;
        padding: 2rem 0;
        margin-top: 3rem;
        text-align: center;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .page-header {
            padding: 2rem 1.5rem;
            text-align: center;
        }

        .page-header h1 {
            font-size: 2rem;
        }

        .permissions-grid {
            grid-template-columns: 1fr;
        }

        .action-buttons {
            flex-direction: column;
            align-items: center;
        }

        .btn-modern {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }
    }
</style>
@endpush

@section('content')
<div class="container-fluid py-4">
    <!-- Header moderne avec gradient -->
    <div class="page-header fade-in-up">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1>
                    <i class="fas fa-user-edit me-3"></i>
                    Modifier le Rôle
                </h1>
                <div class="subtitle">
                    Personnalisez les permissions et les paramètres du rôle "{{ $role->name }}"
                </div>
            </div>
            <a href="{{ route('admin.roles.index') }}" class="back-button">
                <i class="fas fa-arrow-left me-2"></i>
                Retour aux Rôles
            </a>
        </div>
    </div>

    <!-- Messages d'alerte stylés -->
    @if(session('success'))
        <div class="alert alert-success-modern alert-modern fade-in-up">
            <i class="fas fa-check-circle me-2"></i>
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger-modern alert-modern fade-in-up">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ session('error') }}
        </div>
    @endif

    <!-- Carte principale du formulaire -->
    <div class="main-card fade-in-up">
        <!-- Header de la carte -->
        <div class="card-header-custom">
            <h2>
                <div class="role-icon">
                    <i class="fas fa-{{ $role->name === 'admin' ? 'crown' : ($role->name === 'manager' ? 'user-tie' : 'user-shield') }}"></i>
                </div>
                Configuration du Rôle "{{ ucfirst($role->name) }}"
            </h2>
        </div>

        <!-- Formulaire -->
        <form action="{{ route('admin.roles.update', $role) }}" method="POST" id="roleForm">
            @csrf
            @method('PUT')

            <!-- Section informations de base -->
            <div class="form-section">
                <div class="form-group-modern">
                    <label for="name" class="form-label-modern">
                        <i class="fas fa-tag text-primary me-2"></i>
                        Nom du Rôle
                    </label>
                    <input type="text"
                           class="form-control-modern @error('name') is-invalid @enderror"
                           id="name"
                           name="name"
                           value="{{ old('name', $role->name) }}"
                           required
                           placeholder="Entrez le nom du rôle..."
                           {{ $role->name === 'admin' ? 'readonly' : '' }}>
                    @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    @if($role->name === 'admin')
                        <small class="text-muted mt-2 d-block">
                            <i class="fas fa-info-circle me-1"></i>
                            Le nom du rôle administrateur ne peut pas être modifié pour des raisons de sécurité.
                        </small>
                    @endif
                </div>

                <div class="form-group-modern">
                    <label for="description" class="form-label-modern">
                        <i class="fas fa-align-left text-info me-2"></i>
                        Description du Rôle
                    </label>
                    <textarea class="form-control-modern @error('description') is-invalid @enderror"
                              id="description"
                              name="description"
                              rows="3"
                              placeholder="Décrivez les responsabilités et le périmètre de ce rôle...">{{ old('description', $role->description ?? '') }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group-modern">
                            <label for="color" class="form-label-modern">
                                <i class="fas fa-palette text-warning me-2"></i>
                                Couleur du Rôle
                            </label>
                            <input type="color"
                                   class="form-control-modern @error('color') is-invalid @enderror"
                                   id="color"
                                   name="color"
                                   value="{{ old('color', $role->color ?? '#667eea') }}"
                                   style="height: 60px;">
                            @error('color')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group-modern">
                            <label for="priority_level" class="form-label-modern">
                                <i class="fas fa-sort-numeric-up text-success me-2"></i>
                                Niveau de Priorité
                            </label>
                            <select class="form-control-modern @error('priority_level') is-invalid @enderror"
                                    id="priority_level"
                                    name="priority_level">
                                @for($i = 1; $i <= 10; $i++)
                                    <option value="{{ $i }}" {{ old('priority_level', $role->priority_level ?? 5) == $i ? 'selected' : '' }}>
                                        Niveau {{ $i }} {{ $i <= 3 ? '(Faible)' : ($i <= 7 ? '(Moyen)' : '(Élevé)') }}
                                    </option>
                                @endfor
                            </select>
                            @error('priority_level')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section des permissions -->
            <div class="permissions-section">
                <div class="permissions-header">
                    <div class="permissions-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div>
                        <h3 class="permissions-title">Gestion des Permissions</h3>
                        <p class="text-muted mb-0">
                            <strong>Cliquez sur les cartes ci-dessous</strong> pour sélectionner/désélectionner les permissions accordées à ce rôle
                        </p>
                        <small class="text-info d-block mt-1">
                            <i class="fas fa-info-circle me-1"></i>
                            Astuce : Utilisez Ctrl+A pour sélectionner/désélectionner toutes les permissions
                        </small>
                    </div>
                </div>

                @if($role->name === 'admin')
                    <div class="alert alert-warning-modern alert-modern">
                        <i class="fas fa-crown me-2"></i>
                        <strong>Rôle Administrateur :</strong> Ce rôle possède automatiquement toutes les permissions et ne peut pas être modifié.
                    </div>
                @else
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="permission-counter-display">
                            <span id="permission-counter-text" class="badge bg-primary fs-6 px-3 py-2">
                                <i class="fas fa-check-circle me-1"></i>
                                0 permission(s) sélectionnée(s)
                            </span>
                        </div>
                        <div class="permission-actions">
                            <button type="button" class="btn btn-outline-success btn-sm me-2" onclick="selectAllPermissions()">
                                <i class="fas fa-check-double me-1"></i>
                                Tout Sélectionner
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="deselectAllPermissions()">
                                <i class="fas fa-times me-1"></i>
                                Tout Désélectionner
                            </button>
                        </div>
                    </div>
                @endif

                <div class="permissions-grid">
                    @foreach($permissions as $permission)
                        <div class="permission-card {{ in_array($permission->id, old('permissions', $role->permissions->pluck('id')->toArray())) ? 'checked' : '' }}"
                             onclick="togglePermission({{ $permission->id }})">
                            <label class="permission-label" for="permission_{{ $permission->id }}">
                                <input type="checkbox"
                                       class="permission-checkbox"
                                       id="permission_{{ $permission->id }}"
                                       name="permissions[]"
                                       value="{{ $permission->id }}"
                                       {{ in_array($permission->id, old('permissions', $role->permissions->pluck('id')->toArray())) ? 'checked' : '' }}
                                       {{ $role->name === 'admin' ? 'checked disabled' : '' }}
                                       onchange="updatePermissionCard(this)">
                                <span>{{ $permission->name }}</span>
                            </label>
                            <div class="permission-description">
                                @php
                                    $descriptions = [
                                        'users.view' => 'Consulter la liste des utilisateurs',
                                        'users.create' => 'Créer de nouveaux utilisateurs',
                                        'users.edit' => 'Modifier les informations des utilisateurs',
                                        'users.delete' => 'Supprimer des utilisateurs',
                                        'roles.view' => 'Consulter la liste des rôles',
                                        'roles.create' => 'Créer de nouveaux rôles',
                                        'roles.edit' => 'Modifier les rôles existants',
                                        'roles.delete' => 'Supprimer des rôles',
                                        'permissions.view' => 'Consulter les permissions',
                                        'permissions.manage' => 'Gérer les permissions système',
                                        'dashboard.view' => 'Accéder au tableau de bord',
                                        'reports.view' => 'Consulter les rapports',
                                        'reports.export' => 'Exporter les rapports',
                                        'settings.view' => 'Consulter les paramètres',
                                        'settings.edit' => 'Modifier les paramètres système'
                                    ];
                                @endphp
                                {{ $descriptions[$permission->name] ?? 'Permission système pour ' . $permission->name }}
                            </div>
                        </div>
                    @endforeach
                </div>

                @error('permissions')
                    <div class="alert alert-danger-modern alert-modern mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Boutons d'action -->
            <div class="action-buttons">
                <button type="submit"
                        class="btn-modern btn-primary-modern"
                        {{ $role->name === 'admin' ? 'disabled' : '' }}
                        id="saveButton">
                    <i class="fas fa-save"></i>
                    Enregistrer les Modifications
                </button>
                <a href="{{ route('admin.roles.index') }}" class="btn-modern btn-secondary-modern">
                    <i class="fas fa-times"></i>
                    Annuler
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Footer moderne -->
<footer class="footer-modern">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <p class="mb-2">
                    <i class="fas fa-copyright me-1"></i>
                    {{ date('Y') }} GRADIS - Système de Gestion Moderne
                </p>
                <p class="mb-0 text-muted">
                    <i class="fas fa-code me-1"></i>
                    Développé avec ❤️ par MOMK-Solutions
                </p>
            </div>
        </div>
    </div>
</footer>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation d'entrée pour les éléments
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // DÉSACTIVÉ TEMPORAIREMENT - Observer tous les éléments avec animation
    // Le problème était que les éléments étaient masqués et ne redevenaient pas visibles
    document.querySelectorAll('.fade-in-up').forEach(element => {
        // Forcer la visibilité immédiate au lieu de masquer
        element.style.opacity = '1';
        element.style.transform = 'translateY(0)';
        element.style.transition = 'all 0.6s ease-out';
        // observer.observe(element); // Désactivé temporairement
    });

    // Gestion des cartes de permissions
    window.togglePermission = function(permissionId) {
        const checkbox = document.getElementById('permission_' + permissionId);
        const card = checkbox.closest('.permission-card');

        if (!checkbox.disabled) {
            checkbox.checked = !checkbox.checked;
            updatePermissionCard(checkbox);
        }
    };

    window.updatePermissionCard = function(checkbox) {
        const card = checkbox.closest('.permission-card');
        if (checkbox.checked) {
            card.classList.add('checked');
        } else {
            card.classList.remove('checked');
        }

        // Mettre à jour le compteur de permissions
        updatePermissionCounter();
    };

    function updatePermissionCounter() {
        const checkedPermissions = document.querySelectorAll('.permission-checkbox:checked:not(:disabled)').length;
        const totalPermissions = document.querySelectorAll('.permission-checkbox:not(:disabled)').length;

        // Mettre à jour le compteur principal
        const counterText = document.getElementById('permission-counter-text');
        if (counterText) {
            counterText.innerHTML = `<i class="fas fa-check-circle me-1"></i>${checkedPermissions} permission(s) sélectionnée(s)`;

            // Changer la couleur du badge selon le nombre de permissions
            counterText.className = 'badge fs-6 px-3 py-2 ' +
                (checkedPermissions === 0 ? 'bg-warning' :
                 checkedPermissions < totalPermissions / 2 ? 'bg-info' : 'bg-success');
        }

        // Créer ou mettre à jour le compteur détaillé
        let detailedCounter = document.getElementById('detailed-permission-counter');
        if (!detailedCounter) {
            detailedCounter = document.createElement('div');
            detailedCounter.id = 'detailed-permission-counter';
            detailedCounter.className = 'alert alert-info-modern alert-modern mt-3';
            document.querySelector('.permissions-section').appendChild(detailedCounter);
        }

        detailedCounter.innerHTML = `
            <i class="fas fa-info-circle me-2"></i>
            <strong>Résumé :</strong> ${checkedPermissions} permission(s) sélectionnée(s) sur ${totalPermissions} disponibles
            ${checkedPermissions === 0 ? '<br><small class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>Attention : Aucune permission sélectionnée</small>' : ''}
        `;

        // Changer la couleur selon le nombre de permissions
        detailedCounter.className = 'alert alert-modern mt-3 ' +
            (checkedPermissions === 0 ? 'alert-warning-modern' :
             checkedPermissions < totalPermissions / 2 ? 'alert-info-modern' : 'alert-success-modern');
    }

    // Fonction pour sélectionner toutes les permissions
    window.selectAllPermissions = function() {
        const checkboxes = document.querySelectorAll('.permission-checkbox:not(:disabled)');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
            updatePermissionCard(checkbox);
        });
        updatePermissionCounter();
    };

    // Fonction pour désélectionner toutes les permissions
    window.deselectAllPermissions = function() {
        const checkboxes = document.querySelectorAll('.permission-checkbox:not(:disabled)');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
            updatePermissionCard(checkbox);
        });
        updatePermissionCounter();
    };

    // Validation du formulaire
    const form = document.getElementById('roleForm');
    const saveButton = document.getElementById('saveButton');

    form.addEventListener('submit', function(e) {
        const checkedPermissions = document.querySelectorAll('.permission-checkbox:checked:not(:disabled)');

        if (checkedPermissions.length === 0 && !document.querySelector('.permission-checkbox:disabled')) {
            e.preventDefault();

            // Afficher une alerte moderne
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger-modern alert-modern';
            alertDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Attention :</strong> Vous devez sélectionner au moins une permission pour ce rôle.
            `;

            // Insérer l'alerte avant le formulaire
            form.parentNode.insertBefore(alertDiv, form);

            // Faire défiler vers l'alerte
            alertDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // Supprimer l'alerte après 5 secondes
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);

            return false;
        }

        // Animation du bouton de sauvegarde
        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enregistrement...';
        saveButton.disabled = true;
    });

    // Effet de survol sur les cartes de permissions
    document.querySelectorAll('.permission-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Initialiser le compteur de permissions
    updatePermissionCounter();

    // Effet de parallaxe léger sur le header
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const header = document.querySelector('.page-header');
        if (header) {
            header.style.transform = `translateY(${scrolled * 0.1}px)`;
        }
    });

    // Prévisualisation de la couleur en temps réel
    const colorInput = document.getElementById('color');
    if (colorInput) {
        colorInput.addEventListener('input', function() {
            const roleIcon = document.querySelector('.role-icon');
            if (roleIcon) {
                roleIcon.style.background = `linear-gradient(135deg, ${this.value} 0%, ${this.value}dd 100%)`;
            }
        });
    }

    // Animation des boutons
    document.querySelectorAll('.btn-modern').forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.05)';
        });

        button.addEventListener('mouseleave', function() {
            if (!this.disabled) {
                this.style.transform = 'translateY(0) scale(1)';
            }
        });
    });

    // Sélection/désélection rapide avec Ctrl+A
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'a' && e.target.closest('.permissions-section')) {
            e.preventDefault();
            const checkboxes = document.querySelectorAll('.permission-checkbox:not(:disabled)');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);

            checkboxes.forEach(checkbox => {
                checkbox.checked = !allChecked;
                updatePermissionCard(checkbox);
            });
        }
    });
});

// Fonction pour obtenir la description d'une permission
function getPermissionDescription(permissionName) {
    const descriptions = {
        'users.view': 'Consulter la liste des utilisateurs',
        'users.create': 'Créer de nouveaux utilisateurs',
        'users.edit': 'Modifier les informations des utilisateurs',
        'users.delete': 'Supprimer des utilisateurs',
        'roles.view': 'Consulter la liste des rôles',
        'roles.create': 'Créer de nouveaux rôles',
        'roles.edit': 'Modifier les rôles existants',
        'roles.delete': 'Supprimer des rôles',
        'permissions.view': 'Consulter les permissions',
        'permissions.manage': 'Gérer les permissions système',
        'dashboard.view': 'Accéder au tableau de bord',
        'reports.view': 'Consulter les rapports',
        'reports.export': 'Exporter les rapports',
        'settings.view': 'Consulter les paramètres',
        'settings.edit': 'Modifier les paramètres système'
    };

    return descriptions[permissionName] || `Permission système pour ${permissionName}`;
}

// Script pour s'assurer que la page reste visible
// Protection contre les erreurs d'extensions et isolation du code

// Protection immédiate contre les erreurs d'extensions
(function() {
    'use strict';

    // Capturer et ignorer les erreurs d'extensions
    window.addEventListener('error', function(e) {
        // Ignorer les erreurs provenant d'extensions (inpage.js, content scripts, etc.)
        if (e.filename && (
            e.filename.includes('inpage.js') ||
            e.filename.includes('content') ||
            e.filename.includes('extension') ||
            e.filename.includes('chrome-extension') ||
            e.filename.includes('moz-extension')
        )) {
            console.warn('🔇 Erreur d\'extension ignorée:', e.message);
            e.preventDefault();
            return false;
        }
    }, true);

    // Protection contre les erreurs de promesses non gérées
    window.addEventListener('unhandledrejection', function(e) {
        if (e.reason && e.reason.stack && (
            e.reason.stack.includes('inpage.js') ||
            e.reason.stack.includes('extension') ||
            e.reason.stack.includes('chrome-extension')
        )) {
            console.warn('🔇 Erreur de promesse d\'extension ignorée:', e.reason);
            e.preventDefault();
            return false;
        }
    });
})();

document.addEventListener('DOMContentLoaded', function() {
    try {
        console.log('🔧 Page edit role chargée');

        // Forcer la visibilité de la page avec protection d'erreur
        const elements = [
            { selector: '.main-content', name: 'main-content' },
            { selector: '.container-fluid', name: 'container-fluid' },
            { selector: '.form-container', name: 'form-container' },
            { selector: '.page-header', name: 'page-header' },
            { selector: '.role-edit-form', name: 'role-edit-form' }
        ];

        elements.forEach(function(element) {
            try {
                const el = document.querySelector(element.selector);
                if (el) {
                    el.style.setProperty('display', 'block', 'important');
                    el.style.setProperty('visibility', 'visible', 'important');
                    el.style.setProperty('opacity', '1', 'important');
                    el.style.setProperty('position', 'relative', 'important');
                    console.log('✅ Élément visible:', element.name);
                }
            } catch (err) {
                console.warn('⚠️ Erreur lors de la configuration de', element.name, ':', err.message);
            }
        });

        // Vérification périodique de la visibilité
        const checkVisibility = function() {
            try {
                const mainContent = document.querySelector('.main-content');
                if (mainContent && (
                    mainContent.style.display === 'none' ||
                    mainContent.style.visibility === 'hidden' ||
                    mainContent.style.opacity === '0'
                )) {
                    console.warn('🔄 Restauration de la visibilité détectée');
                    mainContent.style.setProperty('display', 'block', 'important');
                    mainContent.style.setProperty('visibility', 'visible', 'important');
                    mainContent.style.setProperty('opacity', '1', 'important');
                }
            } catch (err) {
                console.warn('⚠️ Erreur lors de la vérification de visibilité:', err.message);
            }
        };

        // Vérifier toutes les 2 secondes pendant les 10 premières secondes
        let checkCount = 0;
        const intervalId = setInterval(function() {
            checkVisibility();
            checkCount++;
            if (checkCount >= 5) { // 5 * 2 secondes = 10 secondes
                clearInterval(intervalId);
                console.log('🏁 Surveillance de visibilité terminée');
            }
        }, 2000);

        console.log('✅ Page d\'édition de rôle initialisée correctement');

    } catch (err) {
        console.error('❌ Erreur lors de l\'initialisation de la page:', err.message);
        // Même en cas d'erreur, essayer de forcer la visibilité
        try {
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                mainContent.style.display = 'block';
                mainContent.style.visibility = 'visible';
                mainContent.style.opacity = '1';
            }
        } catch (fallbackErr) {
            console.error('❌ Erreur de fallback:', fallbackErr.message);
        }
    }
});
</script>
@endpush
