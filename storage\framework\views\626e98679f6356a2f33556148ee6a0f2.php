<?php $__env->startSection('title', 'Modifier l\'utilisateur'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4">
    <!-- Header moderne avec breadcrumb -->
    <div class="modern-header-wrapper">
        <div class="modern-header-background"></div>
        <div class="modern-header-content">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-2">
                            <li class="breadcrumb-item">
                                <a href="<?php echo e(route('admin.users.index')); ?>" class="text-white text-decoration-none">
                                    <i class="fas fa-users me-1"></i> Utilisateurs
                                </a>
                            </li>
                            <li class="breadcrumb-item active text-white-50" aria-current="page">Modifier <?php echo e($user->name); ?></li>
                        </ol>
                    </nav>
                    <h1 class="h2 mb-0 text-white fw-bold">
                        <i class="fas fa-user-edit text-warning me-2"></i>
                        Modifier l'Utilisateur
                    </h1>
                    <p class="text-white-50 mb-0">Mettez à jour les informations de <?php echo e($user->name); ?></p>
                </div>
                <div class="d-flex gap-2">
                    <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-light btn-lg shadow-sm">
                        <i class="fas fa-arrow-left me-2"></i> Retour à la liste
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Colonne principale - Formulaire -->
        <div class="col-xl-8 col-lg-7">
            <!-- Formulaire de modification -->
            <div class="card modern-card shadow-lg border-0">
                <div class="card-header bg-gradient-warning text-white">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-white bg-opacity-20 me-3">
                            <i class="fas fa-user-edit text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-0 fw-bold">Modifier les informations</h5>
                            <small class="text-white-50">Modifiez les champs que vous souhaitez mettre à jour</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <form action="<?php echo e(route('admin.users.update', $user)); ?>" method="POST" id="editUserForm" novalidate>
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <!-- Section Informations personnelles -->
                        <div class="form-section mb-4">
                            <div class="section-header mb-3">
                                <h6 class="text-primary fw-bold mb-0">
                                    <i class="fas fa-user me-2"></i>Informations personnelles
                                </h6>
                                <hr class="mt-2 mb-0">
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label fw-semibold">
                                        <i class="fas fa-user text-primary me-1"></i>Nom complet
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-user text-muted"></i>
                                        </span>
                                        <input type="text"
                                               class="form-control border-start-0 <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="name"
                                               name="name"
                                               value="<?php echo e(old('name', $user->name)); ?>"
                                               placeholder="Entrez le nom complet"
                                               required>
                                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="form-text">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Valeur actuelle: <strong><?php echo e($user->name); ?></strong>
                                        </small>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label fw-semibold">
                                        <i class="fas fa-envelope text-primary me-1"></i>Adresse email
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-envelope text-muted"></i>
                                        </span>
                                        <input type="email"
                                               class="form-control border-start-0 <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="email"
                                               name="email"
                                               value="<?php echo e(old('email', $user->email)); ?>"
                                               placeholder="<EMAIL>"
                                               required>
                                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="form-text">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Valeur actuelle: <strong><?php echo e($user->email); ?></strong>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Section Sécurité -->
                        <div class="form-section mb-4">
                            <div class="section-header mb-3">
                                <h6 class="text-success fw-bold mb-0">
                                    <i class="fas fa-shield-alt me-2"></i>Sécurité et authentification
                                </h6>
                                <hr class="mt-2 mb-0">
                            </div>

                            <div class="alert alert-info border-0 shadow-sm">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-info-circle text-info me-2"></i>
                                    <div>
                                        <strong>Modification du mot de passe</strong><br>
                                        <small>Laissez les champs vides si vous ne souhaitez pas modifier le mot de passe actuel.</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label fw-semibold">
                                        <i class="fas fa-lock text-success me-1"></i>Nouveau mot de passe
                                        <span class="text-muted">(optionnel)</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-lock text-muted"></i>
                                        </span>
                                        <input type="password"
                                               class="form-control border-start-0 border-end-0 <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="password"
                                               name="password"
                                               placeholder="Nouveau mot de passe">
                                        <button class="btn btn-outline-secondary border-start-0" type="button" id="togglePassword">
                                            <i class="fas fa-eye" id="passwordIcon"></i>
                                        </button>
                                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="form-text">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Minimum 8 caractères si vous souhaitez le modifier
                                        </small>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="password_confirmation" class="form-label fw-semibold">
                                        <i class="fas fa-check-circle text-success me-1"></i>Confirmer le nouveau mot de passe
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-check-circle text-muted"></i>
                                        </span>
                                        <input type="password"
                                               class="form-control border-start-0 border-end-0"
                                               id="password_confirmation"
                                               name="password_confirmation"
                                               placeholder="Confirmer le nouveau mot de passe">
                                        <button class="btn btn-outline-secondary border-start-0" type="button" id="togglePasswordConfirm">
                                            <i class="fas fa-eye" id="passwordConfirmIcon"></i>
                                        </button>
                                    </div>
                                    <div id="passwordMatch" class="form-text"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Section Rôles et permissions -->
                        <div class="form-section mb-4">
                            <div class="section-header mb-3">
                                <h6 class="text-warning fw-bold mb-0">
                                    <i class="fas fa-user-tag me-2"></i>Rôles et permissions
                                </h6>
                                <hr class="mt-2 mb-0">
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    <i class="fas fa-users-cog text-warning me-1"></i>Modifier les rôles
                                    <span class="text-danger">*</span>
                                </label>

                                <!-- Rôles actuels -->
                                <div class="current-roles mb-3 p-3 bg-light rounded">
                                    <h6 class="text-muted mb-2">
                                        <i class="fas fa-tag me-1"></i>Rôles actuels :
                                    </h6>
                                    <div class="d-flex flex-wrap gap-2">
                                        <?php $__empty_1 = true; $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currentRole): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                            <span class="badge bg-primary">
                                                <i class="fas fa-user-tag me-1"></i><?php echo e(ucfirst($currentRole->name)); ?>

                                            </span>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                            <span class="text-muted">Aucun rôle assigné</span>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="roles-container">
                                    <div class="row g-3">
                                        <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="col-md-6 col-lg-4">
                                                <div class="role-card">
                                                    <input class="form-check-input role-checkbox"
                                                           type="checkbox"
                                                           name="roles[]"
                                                           value="<?php echo e($role->name); ?>"
                                                           id="role_<?php echo e($role->id); ?>"
                                                           <?php echo e(in_array($role->name, old('roles', $user->roles->pluck('name')->toArray())) ? 'checked' : ''); ?>>
                                                    <label class="role-label" for="role_<?php echo e($role->id); ?>">
                                                        <div class="role-icon">
                                                            <i class="fas fa-<?php echo e($role->name === 'admin' ? 'crown' : ($role->name === 'manager' ? 'user-tie' : 'user')); ?>"></i>
                                                        </div>
                                                        <div class="role-info">
                                                            <div class="role-name"><?php echo e(ucfirst($role->name)); ?></div>
                                                            <div class="role-description">
                                                                <?php switch($role->name):
                                                                    case ('admin'): ?>
                                                                        Accès complet au système
                                                                        <?php break; ?>
                                                                    <?php case ('manager'): ?>
                                                                        Gestion des équipes
                                                                        <?php break; ?>
                                                                    <?php case ('user'): ?>
                                                                        Utilisateur standard
                                                                        <?php break; ?>
                                                                    <?php default: ?>
                                                                        Rôle <?php echo e($role->name); ?>

                                                                <?php endswitch; ?>
                                                            </div>
                                                        </div>
                                                        <div class="role-check">
                                                            <i class="fas fa-check"></i>
                                                        </div>
                                                        <?php if(in_array($role->name, $user->roles->pluck('name')->toArray())): ?>
                                                            <div class="role-current">
                                                                <i class="fas fa-star"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                    </label>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                                <?php $__errorArgs = ['roles'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger mt-2">
                                        <i class="fas fa-exclamation-triangle me-1"></i><?php echo e($message); ?>

                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="form-actions">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <button type="button" class="btn btn-outline-info btn-lg w-100" id="previewBtn">
                                        <i class="fas fa-eye me-2"></i>Aperçu
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <button type="button" class="btn btn-outline-secondary btn-lg w-100" id="resetBtn">
                                        <i class="fas fa-undo me-2"></i>Réinitialiser
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-warning btn-lg w-100" id="submitBtn">
                                        <span class="btn-text">
                                            <i class="fas fa-save me-2"></i>Mettre à jour
                                        </span>
                                        <span class="btn-loading d-none">
                                            <i class="fas fa-spinner fa-spin me-2"></i>Mise à jour...
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Carte d'aperçu des modifications (masquée par défaut) -->
            <div class="card modern-card shadow-lg border-0 mt-4 d-none" id="previewCard">
                <div class="card-header bg-gradient-info text-white">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-white bg-opacity-20 me-3">
                            <i class="fas fa-eye text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-0 fw-bold">Aperçu des modifications</h5>
                            <small class="text-white-50">Vérifiez les changements avant la mise à jour</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">
                                <i class="fas fa-arrow-left text-danger me-1"></i>Valeurs actuelles
                            </h6>
                            <div class="current-values">
                                <div class="value-item mb-2">
                                    <strong>Nom:</strong> <?php echo e($user->name); ?>

                                </div>
                                <div class="value-item mb-2">
                                    <strong>Email:</strong> <?php echo e($user->email); ?>

                                </div>
                                <div class="value-item mb-2">
                                    <strong>Rôles:</strong>
                                    <?php $__empty_1 = true; $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <span class="badge bg-secondary me-1"><?php echo e(ucfirst($role->name)); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <span class="text-muted">Aucun</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">
                                <i class="fas fa-arrow-right text-success me-1"></i>Nouvelles valeurs
                            </h6>
                            <div class="new-values">
                                <div class="value-item mb-2">
                                    <strong>Nom:</strong> <span id="previewName"><?php echo e($user->name); ?></span>
                                </div>
                                <div class="value-item mb-2">
                                    <strong>Email:</strong> <span id="previewEmail"><?php echo e($user->email); ?></span>
                                </div>
                                <div class="value-item mb-2">
                                    <strong>Rôles:</strong> <span id="previewRoles"></span>
                                </div>
                                <div class="value-item mb-2">
                                    <strong>Mot de passe:</strong> <span id="previewPassword">Inchangé</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Colonne latérale - Informations utilisateur -->
        <div class="col-xl-4 col-lg-5">
            <!-- Profil utilisateur -->
            <div class="card modern-card shadow-lg border-0 mb-4">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-white bg-opacity-20 me-3">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-0 fw-bold">Profil utilisateur</h5>
                            <small class="text-white-50">Informations actuelles</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4 text-center">
                    <div class="user-avatar mb-3">
                        <div class="avatar-large">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <h5 class="mb-1"><?php echo e($user->name); ?></h5>
                    <p class="text-muted mb-3"><?php echo e($user->email); ?></p>

                    <div class="user-stats row text-center">
                        <div class="col-6">
                            <div class="stat-box">
                                <div class="stat-number"><?php echo e($user->roles->count()); ?></div>
                                <div class="stat-label">Rôles</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-box">
                                <div class="stat-number">
                                    <?php if($user->is_active ?? true): ?>
                                        <span class="text-success">Actif</span>
                                    <?php else: ?>
                                        <span class="text-danger">Inactif</span>
                                    <?php endif; ?>
                                </div>
                                <div class="stat-label">Statut</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Informations système -->
            <div class="card modern-card shadow-lg border-0 mb-4">
                <div class="card-header bg-gradient-secondary text-white">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-white bg-opacity-20 me-3">
                            <i class="fas fa-info-circle text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-0 fw-bold">Informations système</h5>
                            <small class="text-white-50">Données techniques</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="info-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">ID utilisateur:</span>
                            <span class="fw-bold">#<?php echo e($user->id); ?></span>
                        </div>
                    </div>
                    <div class="info-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Créé le:</span>
                            <span class="fw-bold"><?php echo e($user->created_at->format('d/m/Y H:i')); ?></span>
                        </div>
                    </div>
                    <div class="info-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Modifié le:</span>
                            <span class="fw-bold"><?php echo e($user->updated_at->format('d/m/Y H:i')); ?></span>
                        </div>
                    </div>
                    <?php if($user->email_verified_at): ?>
                        <div class="info-item mb-3">
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">Email vérifié:</span>
                                <span class="text-success fw-bold">
                                    <i class="fas fa-check-circle me-1"></i>Oui
                                </span>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="info-item mb-3">
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">Email vérifié:</span>
                                <span class="text-warning fw-bold">
                                    <i class="fas fa-exclamation-triangle me-1"></i>Non
                                </span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Actions rapides -->
            <div class="card modern-card shadow-lg border-0">
                <div class="card-header bg-gradient-danger text-white">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-white bg-opacity-20 me-3">
                            <i class="fas fa-tools text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-0 fw-bold">Actions rapides</h5>
                            <small class="text-white-50">Opérations avancées</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="d-grid gap-2">
                        <?php if(!$user->email_verified_at): ?>
                            <button class="btn btn-outline-success btn-sm">
                                <i class="fas fa-envelope-check me-1"></i>
                                Marquer email comme vérifié
                            </button>
                        <?php endif; ?>
                        <button class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-key me-1"></i>
                            Réinitialiser mot de passe
                        </button>
                        <button class="btn btn-outline-info btn-sm">
                            <i class="fas fa-history me-1"></i>
                            Voir l'historique
                        </button>
                        <hr>
                        <button class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-user-slash me-1"></i>
                            Désactiver le compte
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light">
    <div class="container text-center">
        <span class="text-muted">© <?php echo e(date('Y')); ?> GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>
<?php $__env->startPush('styles'); ?>
<style>
/* ===== VARIABLES CSS ===== */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --secondary-gradient: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    --light-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

/* ===== HEADER MODERNE ===== */
.modern-header-wrapper {
    position: relative;
    margin: -1.5rem -1.5rem 2rem -1.5rem;
    overflow: hidden;
    border-radius: 0 0 20px 20px;
}

.modern-header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--warning-gradient);
    opacity: 0.9;
}

.modern-header-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

.modern-header-content {
    position: relative;
    z-index: 2;
    padding: 2rem;
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: rgba(255, 255, 255, 0.7);
    font-weight: bold;
}

/* ===== CARTES MODERNES ===== */
.modern-card {
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
    animation: slideInUp 0.6s ease-out;
}

.modern-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
}

.card-header.bg-gradient-primary {
    background: var(--primary-gradient) !important;
    border: none;
    padding: 1.5rem;
}

.card-header.bg-gradient-warning {
    background: var(--warning-gradient) !important;
    border: none;
    padding: 1.5rem;
}

.card-header.bg-gradient-info {
    background: var(--info-gradient) !important;
    border: none;
    padding: 1.5rem;
}

.card-header.bg-gradient-secondary {
    background: var(--secondary-gradient) !important;
    border: none;
    padding: 1.5rem;
}

.card-header.bg-gradient-danger {
    background: var(--danger-gradient) !important;
    border: none;
    padding: 1.5rem;
}

.icon-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

/* ===== SECTIONS DE FORMULAIRE ===== */
.form-section {
    position: relative;
    padding: 1.5rem;
    background: linear-gradient(145deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 15px;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.section-header h6 {
    position: relative;
    display: inline-block;
    padding: 0.5rem 1rem;
    background: white;
    border-radius: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* ===== CHAMPS DE SAISIE AMÉLIORÉS ===== */
.input-group {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.input-group:focus-within {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.2);
}

.input-group-text {
    border: none;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
}

.form-control {
    border: none;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    box-shadow: none;
    border-color: transparent;
    background: rgba(102, 126, 234, 0.05);
}

/* ===== RÔLES ACTUELS ===== */
.current-roles {
    border: 2px dashed rgba(240, 147, 251, 0.3);
    background: linear-gradient(145deg, rgba(240, 147, 251, 0.05), rgba(245, 87, 108, 0.05));
}

/* ===== CARTES DE RÔLES ===== */
.roles-container {
    padding: 1rem;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 15px;
    border: 2px dashed rgba(240, 147, 251, 0.2);
}

.role-card {
    position: relative;
    transition: all 0.3s ease;
}

.role-checkbox {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.role-label {
    display: block;
    padding: 1.5rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.role-label::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(240, 147, 251, 0.1), transparent);
    transition: left 0.5s ease;
}

.role-label:hover::before {
    left: 100%;
}

.role-label:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-color: #f093fb;
}

.role-checkbox:checked + .role-label {
    background: var(--warning-gradient);
    color: white;
    border-color: #f093fb;
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(240, 147, 251, 0.3);
}

.role-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.role-checkbox:checked + .role-label .role-icon {
    opacity: 1;
    transform: scale(1.1);
}

.role-name {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.role-description {
    font-size: 0.85rem;
    opacity: 0.8;
}

.role-check {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 25px;
    height: 25px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
}

.role-checkbox:checked + .role-label .role-check {
    opacity: 1;
    transform: scale(1);
}

.role-current {
    position: absolute;
    top: 10px;
    left: 10px;
    width: 25px;
    height: 25px;
    background: #ffc107;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    animation: pulse 2s infinite;
}

/* ===== BOUTONS D'ACTION ===== */
.form-actions {
    padding: 2rem;
    background: linear-gradient(145deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 15px;
    margin-top: 2rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-warning {
    background: var(--warning-gradient);
    border: none;
    color: white;
    box-shadow: 0 5px 15px rgba(240, 147, 251, 0.3);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
    color: white;
}

.btn-outline-info {
    border: 2px solid #17a2b8;
    color: #17a2b8;
    background: white;
}

.btn-outline-info:hover {
    background: #17a2b8;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(23, 162, 184, 0.3);
}

.btn-outline-secondary {
    border: 2px solid #6c757d;
    color: #6c757d;
    background: white;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
}

/* ===== PROFIL UTILISATEUR ===== */
.user-avatar {
    margin-bottom: 1rem;
}

.avatar-large {
    width: 80px;
    height: 80px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin: 0 auto;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.user-stats {
    margin-top: 1rem;
}

.stat-box {
    padding: 1rem;
    background: linear-gradient(145deg, #f8f9fa, #ffffff);
    border-radius: 10px;
    margin-bottom: 0.5rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
}

.stat-label {
    font-size: 0.85rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== INFORMATIONS SYSTÈME ===== */
.info-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.info-item:last-child {
    border-bottom: none;
}

/* ===== APERÇU DES MODIFICATIONS ===== */
.current-values, .new-values {
    padding: 1rem;
    border-radius: 10px;
}

.current-values {
    background: linear-gradient(145deg, #fff5f5, #ffffff);
    border: 1px solid rgba(220, 38, 38, 0.1);
}

.new-values {
    background: linear-gradient(145deg, #f0fff4, #ffffff);
    border: 1px solid rgba(34, 197, 94, 0.1);
}

.value-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.value-item:last-child {
    border-bottom: none;
}

/* ===== ANIMATIONS ===== */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.btn-loading {
    animation: pulse 1.5s infinite;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .modern-header-content {
        padding: 1.5rem;
    }

    .form-section {
        padding: 1rem;
    }

    .role-label {
        padding: 1rem;
    }

    .form-actions {
        padding: 1.5rem;
    }

    .avatar-large {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* ===== ALERTES PERSONNALISÉES ===== */
.alert {
    border-radius: 12px;
    border: none;
    padding: 1rem 1.5rem;
}

.alert-info {
    background: linear-gradient(145deg, rgba(79, 172, 254, 0.1), rgba(0, 242, 254, 0.1));
    color: #0c5460;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // ===== ÉLÉMENTS DU DOM =====
    const form = document.getElementById('editUserForm');
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const passwordConfirmInput = document.getElementById('password_confirmation');
    const togglePasswordBtn = document.getElementById('togglePassword');
    const togglePasswordConfirmBtn = document.getElementById('togglePasswordConfirm');
    const passwordIcon = document.getElementById('passwordIcon');
    const passwordConfirmIcon = document.getElementById('passwordConfirmIcon');
    const passwordMatch = document.getElementById('passwordMatch');
    const previewBtn = document.getElementById('previewBtn');
    const resetBtn = document.getElementById('resetBtn');
    const previewCard = document.getElementById('previewCard');
    const submitBtn = document.getElementById('submitBtn');
    const roleCheckboxes = document.querySelectorAll('.role-checkbox');

    // Éléments d'aperçu
    const previewName = document.getElementById('previewName');
    const previewEmail = document.getElementById('previewEmail');
    const previewRoles = document.getElementById('previewRoles');
    const previewPassword = document.getElementById('previewPassword');

    // Valeurs originales pour la réinitialisation
    const originalValues = {
        name: '<?php echo e($user->name); ?>',
        email: '<?php echo e($user->email); ?>',
        roles: <?php echo json_encode($user->roles->pluck('name')->toArray(), 15, 512) ?>
    };

    // ===== TOGGLE VISIBILITÉ MOT DE PASSE =====
    if (togglePasswordBtn) {
        togglePasswordBtn.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            passwordIcon.classList.toggle('fa-eye');
            passwordIcon.classList.toggle('fa-eye-slash');
        });
    }

    if (togglePasswordConfirmBtn) {
        togglePasswordConfirmBtn.addEventListener('click', function() {
            const type = passwordConfirmInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordConfirmInput.setAttribute('type', type);
            passwordConfirmIcon.classList.toggle('fa-eye');
            passwordConfirmIcon.classList.toggle('fa-eye-slash');
        });
    }

    // ===== VALIDATION MOT DE PASSE EN TEMPS RÉEL =====
    function checkPasswordMatch() {
        const password = passwordInput.value;
        const confirmPassword = passwordConfirmInput.value;

        if (password === '' && confirmPassword === '') {
            passwordMatch.innerHTML = '';
            passwordConfirmInput.classList.remove('is-invalid', 'is-valid');
            return;
        }

        if (password === '' && confirmPassword !== '') {
            passwordMatch.innerHTML = '<small class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>Veuillez d\'abord saisir un nouveau mot de passe</small>';
            passwordConfirmInput.classList.remove('is-valid');
            passwordConfirmInput.classList.add('is-invalid');
            return;
        }

        if (password !== '' && confirmPassword === '') {
            passwordMatch.innerHTML = '<small class="text-info"><i class="fas fa-info-circle me-1"></i>Veuillez confirmer le nouveau mot de passe</small>';
            return;
        }

        if (password === confirmPassword) {
            passwordMatch.innerHTML = '<small class="text-success"><i class="fas fa-check-circle me-1"></i>Les mots de passe correspondent</small>';
            passwordConfirmInput.classList.remove('is-invalid');
            passwordConfirmInput.classList.add('is-valid');
        } else {
            passwordMatch.innerHTML = '<small class="text-danger"><i class="fas fa-times-circle me-1"></i>Les mots de passe ne correspondent pas</small>';
            passwordConfirmInput.classList.remove('is-valid');
            passwordConfirmInput.classList.add('is-invalid');
        }
    }

    passwordInput.addEventListener('input', checkPasswordMatch);
    passwordConfirmInput.addEventListener('input', checkPasswordMatch);

    // ===== VALIDATION FORCE MOT DE PASSE =====
    passwordInput.addEventListener('input', function() {
        const password = this.value;
        if (password.length > 0) {
            const strength = calculatePasswordStrength(password);
            updatePasswordStrengthIndicator(strength);
        } else {
            removePasswordStrengthIndicator();
        }
    });

    function calculatePasswordStrength(password) {
        let strength = 0;
        if (password.length >= 8) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        return strength;
    }

    function updatePasswordStrengthIndicator(strength) {
        removePasswordStrengthIndicator();

        const strengthDiv = document.createElement('div');
        strengthDiv.className = 'password-strength mt-1';

        let strengthText = '';
        let strengthClass = '';

        switch(strength) {
            case 0:
            case 1:
                strengthText = 'Très faible';
                strengthClass = 'text-danger';
                break;
            case 2:
                strengthText = 'Faible';
                strengthClass = 'text-warning';
                break;
            case 3:
                strengthText = 'Moyen';
                strengthClass = 'text-info';
                break;
            case 4:
                strengthText = 'Fort';
                strengthClass = 'text-success';
                break;
            case 5:
                strengthText = 'Très fort';
                strengthClass = 'text-success fw-bold';
                break;
        }

        strengthDiv.innerHTML = `<small class="${strengthClass}"><i class="fas fa-shield-alt me-1"></i>Force: ${strengthText}</small>`;
        passwordInput.parentElement.parentElement.appendChild(strengthDiv);
    }

    function removePasswordStrengthIndicator() {
        const indicator = passwordInput.parentElement.parentElement.querySelector('.password-strength');
        if (indicator) indicator.remove();
    }

    // ===== DÉTECTION DES CHANGEMENTS =====
    function hasChanges() {
        const currentName = nameInput.value.trim();
        const currentEmail = emailInput.value.trim();
        const currentRoles = Array.from(roleCheckboxes)
            .filter(checkbox => checkbox.checked)
            .map(checkbox => checkbox.value)
            .sort();
        const hasPasswordChange = passwordInput.value.length > 0;

        const nameChanged = currentName !== originalValues.name;
        const emailChanged = currentEmail !== originalValues.email;
        const rolesChanged = JSON.stringify(currentRoles) !== JSON.stringify(originalValues.roles.sort());

        return nameChanged || emailChanged || rolesChanged || hasPasswordChange;
    }

    // ===== APERÇU EN TEMPS RÉEL =====
    function updatePreview() {
        // Nom
        const newName = nameInput.value.trim() || originalValues.name;
        previewName.textContent = newName;
        previewName.className = newName !== originalValues.name ? 'text-success fw-bold' : '';

        // Email
        const newEmail = emailInput.value.trim() || originalValues.email;
        previewEmail.textContent = newEmail;
        previewEmail.className = newEmail !== originalValues.email ? 'text-success fw-bold' : '';

        // Mot de passe
        if (passwordInput.value.length > 0) {
            previewPassword.innerHTML = '<span class="text-success fw-bold">Sera modifié</span>';
        } else {
            previewPassword.innerHTML = '<span class="text-muted">Inchangé</span>';
        }

        // Rôles
        const selectedRoles = Array.from(roleCheckboxes)
            .filter(checkbox => checkbox.checked)
            .map(checkbox => checkbox.value);

        previewRoles.innerHTML = '';
        if (selectedRoles.length > 0) {
            selectedRoles.forEach(role => {
                const badge = document.createElement('span');
                const isOriginal = originalValues.roles.includes(role);
                badge.className = `badge ${isOriginal ? 'bg-secondary' : 'bg-success'} me-1 mb-1`;
                badge.innerHTML = `<i class="fas fa-user-tag me-1"></i>${role.charAt(0).toUpperCase() + role.slice(1)}`;
                if (!isOriginal) {
                    badge.title = 'Nouveau rôle';
                }
                previewRoles.appendChild(badge);
            });

            // Afficher les rôles supprimés
            originalValues.roles.forEach(role => {
                if (!selectedRoles.includes(role)) {
                    const badge = document.createElement('span');
                    badge.className = 'badge bg-danger me-1 mb-1';
                    badge.innerHTML = `<i class="fas fa-times me-1"></i>${role.charAt(0).toUpperCase() + role.slice(1)}`;
                    badge.title = 'Rôle supprimé';
                    previewRoles.appendChild(badge);
                }
            });
        } else {
            previewRoles.innerHTML = '<span class="text-muted">Aucun rôle sélectionné</span>';
        }

        // Mettre à jour l'état du bouton de soumission
        if (hasChanges()) {
            submitBtn.classList.remove('btn-outline-warning');
            submitBtn.classList.add('btn-warning');
            submitBtn.disabled = false;
        } else {
            submitBtn.classList.remove('btn-warning');
            submitBtn.classList.add('btn-outline-warning');
            submitBtn.disabled = true;
        }
    }

    // Écouter les changements pour l'aperçu
    nameInput.addEventListener('input', updatePreview);
    emailInput.addEventListener('input', updatePreview);
    passwordInput.addEventListener('input', updatePreview);
    roleCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updatePreview);
    });

    // ===== BOUTON APERÇU =====
    previewBtn.addEventListener('click', function() {
        updatePreview();
        previewCard.classList.toggle('d-none');

        if (!previewCard.classList.contains('d-none')) {
            previewCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
            this.innerHTML = '<i class="fas fa-eye-slash me-2"></i>Masquer l\'aperçu';
            this.classList.remove('btn-outline-info');
            this.classList.add('btn-info');
        } else {
            this.innerHTML = '<i class="fas fa-eye me-2"></i>Aperçu';
            this.classList.remove('btn-info');
            this.classList.add('btn-outline-info');
        }
    });

    // ===== BOUTON RÉINITIALISER =====
    resetBtn.addEventListener('click', function() {
        if (confirm('Êtes-vous sûr de vouloir réinitialiser tous les champs à leurs valeurs originales ?')) {
            // Réinitialiser les champs
            nameInput.value = originalValues.name;
            emailInput.value = originalValues.email;
            passwordInput.value = '';
            passwordConfirmInput.value = '';

            // Réinitialiser les rôles
            roleCheckboxes.forEach(checkbox => {
                checkbox.checked = originalValues.roles.includes(checkbox.value);
            });

            // Nettoyer les validations
            form.querySelectorAll('.is-invalid, .is-valid').forEach(el => {
                el.classList.remove('is-invalid', 'is-valid');
            });

            // Nettoyer les indicateurs
            passwordMatch.innerHTML = '';
            removePasswordStrengthIndicator();

            // Mettre à jour l'aperçu
            updatePreview();

            // Animation de confirmation
            this.innerHTML = '<i class="fas fa-check me-2"></i>Réinitialisé !';
            this.classList.add('btn-success');
            this.classList.remove('btn-outline-secondary');

            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-undo me-2"></i>Réinitialiser';
                this.classList.remove('btn-success');
                this.classList.add('btn-outline-secondary');
            }, 2000);
        }
    });

    // ===== VALIDATION FORMULAIRE =====
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!hasChanges()) {
            alert('Aucune modification détectée. Veuillez apporter des changements avant de soumettre.');
            return;
        }

        // Vérifier que tous les champs requis sont remplis
        let isValid = true;
        const requiredFields = [nameInput, emailInput];

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        // Vérifier qu'au moins un rôle est sélectionné
        const hasSelectedRole = Array.from(roleCheckboxes).some(checkbox => checkbox.checked);
        if (!hasSelectedRole) {
            alert('Veuillez sélectionner au moins un rôle pour l\'utilisateur.');
            isValid = false;
        }

        // Vérifier que les mots de passe correspondent (si remplis)
        if (passwordInput.value && passwordInput.value !== passwordConfirmInput.value) {
            passwordConfirmInput.classList.add('is-invalid');
            isValid = false;
        }

        if (isValid) {
            // Afficher l'état de chargement
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoading = submitBtn.querySelector('.btn-loading');

            btnText.classList.add('d-none');
            btnLoading.classList.remove('d-none');
            submitBtn.disabled = true;

            // Soumettre le formulaire
            setTimeout(() => {
                this.submit();
            }, 500);
        } else {
            // Faire défiler vers le premier champ invalide
            const firstInvalid = form.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstInvalid.focus();
            }
        }
    });

    // ===== ANIMATIONS D'ENTRÉE =====
    const cards = document.querySelectorAll('.modern-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // ===== VALIDATION EMAIL EN TEMPS RÉEL =====
    emailInput.addEventListener('input', function() {
        const email = this.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (email && !emailRegex.test(email)) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });

    // ===== INITIALISATION =====
    updatePreview();

    // Ajouter des effets de focus améliorés
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'translateY(-2px)';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'translateY(0)';
        });
    });

    // ===== AVERTISSEMENT AVANT FERMETURE =====
    window.addEventListener('beforeunload', function(e) {
        if (hasChanges()) {
            e.preventDefault();
            e.returnValue = '';
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin_minimal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/admin/users/edit.blade.php ENDPATH**/ ?>