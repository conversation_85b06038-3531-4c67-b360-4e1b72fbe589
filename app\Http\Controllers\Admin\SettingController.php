<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Models\Setting;
use Carbon\Carbon;
use ZipArchive;

class SettingController extends Controller
{
    public function index()
    {
        // Récupérer les paramètres depuis la base de données ou les valeurs par défaut
        $settings = [
            'app_name' => $this->getSetting('app_name', config('app.name')),
            'app_email' => $this->getSetting('app_email', config('mail.from.address')),
            'app_currency' => $this->getSetting('app_currency', 'FCFA'),
            'tax_rate' => $this->getSetting('tax_rate', 18),
            'backup_enabled' => $this->getSetting('backup_enabled', true),
        ];

        // Récupérer l'historique des sauvegardes
        $backups = $this->getBackupHistory();

        return view('admin.settings.index', compact('settings', 'backups'));
    }

    /**
     * Récupérer une valeur de paramètre
     */
    private function getSetting($key, $default = null)
    {
        $setting = Setting::where('key', $key)->where('group', 'general')->first();
        return $setting ? $setting->value : $default;
    }

    public function update(Request $request)
    {
        try {
            $validated = $request->validate([
                'app_name' => 'required|string|max:255',
                'app_email' => 'required|email',
                'app_currency' => 'required|string|max:10',
                'tax_rate' => 'required|numeric|min:0|max:100',
                'backup_enabled' => 'boolean'
            ]);

            // Convertir backup_enabled en boolean si non présent
            $validated['backup_enabled'] = $request->has('backup_enabled');

            // Mettre à jour les paramètres dans la base de données
            foreach ($validated as $key => $value) {
                $this->updateSetting($key, $value);
            }

            // Vider le cache des paramètres
            Cache::forget('settings');

            return redirect()->route('admin.settings.index')
                ->with('success', 'Paramètres mis à jour avec succès.');
        } catch (\Exception $e) {
            Log::error('Erreur lors de la mise à jour des paramètres: ' . $e->getMessage());
            return redirect()->route('admin.settings.index')
                ->with('error', 'Erreur lors de la mise à jour des paramètres: ' . $e->getMessage());
        }
    }

    public function backup(Request $request)
    {
        try {
            // Créer le dossier de sauvegarde s'il n'existe pas
            $backupPath = storage_path('app/backups');
            if (!File::exists($backupPath)) {
                File::makeDirectory($backupPath, 0755, true);
            }

            $timestamp = date('Y-m-d-H-i-s');
            $filename = 'backup-' . $timestamp;

            // Options de sauvegarde
            $includeFiles = $request->has('include_files');
            $includeLogs = $request->has('include_logs');
            $compress = $request->has('compress_backup');

            // Créer la sauvegarde de la base de données
            $sqlFile = $backupPath . '/' . $filename . '.sql';
            $this->createDatabaseBackup($sqlFile);

            $finalFile = $sqlFile;

            // Si compression ou inclusion de fichiers, créer un ZIP
            if ($compress || $includeFiles || $includeLogs) {
                $zipFile = $backupPath . '/' . $filename . '.zip';
                $zip = new ZipArchive();

                if ($zip->open($zipFile, ZipArchive::CREATE) === TRUE) {
                    // Ajouter le fichier SQL
                    $zip->addFile($sqlFile, 'database.sql');

                    // Ajouter les fichiers uploadés si demandé
                    if ($includeFiles) {
                        $this->addFilesToZip($zip, storage_path('app/public'), 'uploads/');
                    }

                    // Ajouter les logs si demandé
                    if ($includeLogs) {
                        $this->addFilesToZip($zip, storage_path('logs'), 'logs/');
                    }

                    $zip->close();

                    // Supprimer le fichier SQL temporaire
                    File::delete($sqlFile);
                    $finalFile = $zipFile;
                    $filename .= '.zip';
                } else {
                    throw new \Exception('Impossible de créer le fichier ZIP');
                }
            } else {
                $filename .= '.sql';
            }

            // Enregistrer l'information de sauvegarde
            $this->logBackup($filename, File::size($finalFile));

            return response()->download($finalFile)->deleteFileAfterSend(false);

        } catch (\Exception $e) {
            Log::error('Erreur lors de la création de la sauvegarde: ' . $e->getMessage());
            return redirect()->route('admin.settings.index')
                ->with('error', 'Erreur lors de la création de la sauvegarde : ' . $e->getMessage());
        }
    }

    public function restore(Request $request)
    {
        $request->validate([
            'backup_file' => 'required|file|mimes:sql,zip'
        ]);

        try {
            $file = $request->file('backup_file');
            $tempPath = storage_path('app/temp');

            // Créer le dossier temp s'il n'existe pas
            if (!File::exists($tempPath)) {
                File::makeDirectory($tempPath, 0755, true);
            }

            $filePath = $file->storeAs('temp', $file->getClientOriginalName());
            $fullPath = storage_path('app/' . $filePath);

            // Vérifier le type de fichier
            $extension = $file->getClientOriginalExtension();

            if ($extension === 'zip') {
                // Extraire le ZIP et trouver le fichier SQL
                $zip = new ZipArchive();
                if ($zip->open($fullPath) === TRUE) {
                    $extractPath = $tempPath . '/extracted_' . time();
                    $zip->extractTo($extractPath);
                    $zip->close();

                    // Chercher le fichier SQL
                    $sqlFile = $extractPath . '/database.sql';
                    if (!File::exists($sqlFile)) {
                        throw new \Exception('Fichier database.sql non trouvé dans l\'archive');
                    }

                    $this->restoreDatabase($sqlFile);

                    // Nettoyer les fichiers temporaires
                    File::deleteDirectory($extractPath);
                } else {
                    throw new \Exception('Impossible d\'ouvrir le fichier ZIP');
                }
            } else {
                // Fichier SQL direct
                $this->restoreDatabase($fullPath);
            }

            // Nettoyer le fichier temporaire
            Storage::delete($filePath);

            return redirect()->route('admin.settings.index')
                ->with('success', 'Base de données restaurée avec succès.');

        } catch (\Exception $e) {
            Log::error('Erreur lors de la restauration: ' . $e->getMessage());
            return redirect()->route('admin.settings.index')
                ->with('error', 'Erreur lors de la restauration : ' . $e->getMessage());
        }
    }

    /**
     * Télécharger une sauvegarde existante
     */
    public function downloadBackup($filename)
    {
        try {
            $filePath = storage_path('app/backups/' . $filename);

            if (!File::exists($filePath)) {
                return redirect()->route('admin.settings.index')
                    ->with('error', 'Fichier de sauvegarde non trouvé.');
            }

            return response()->download($filePath);
        } catch (\Exception $e) {
            Log::error('Erreur lors du téléchargement: ' . $e->getMessage());
            return redirect()->route('admin.settings.index')
                ->with('error', 'Erreur lors du téléchargement : ' . $e->getMessage());
        }
    }

    /**
     * Supprimer une sauvegarde
     */
    public function deleteBackup($filename)
    {
        try {
            $filePath = storage_path('app/backups/' . $filename);

            if (File::exists($filePath)) {
                File::delete($filePath);
                return response()->json(['success' => true, 'message' => 'Sauvegarde supprimée avec succès.']);
            }

            return response()->json(['success' => false, 'message' => 'Fichier non trouvé.'], 404);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la suppression: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Erreur lors de la suppression.'], 500);
        }
    }

    /**
     * Vider le cache
     */
    public function clearCache()
    {
        try {
            Artisan::call('cache:clear');
            Artisan::call('config:clear');
            Artisan::call('view:clear');
            Artisan::call('route:clear');

            return response()->json(['success' => true, 'message' => 'Cache vidé avec succès.']);
        } catch (\Exception $e) {
            Log::error('Erreur lors du vidage du cache: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Erreur lors du vidage du cache.'], 500);
        }
    }

    /**
     * Optimiser la base de données
     */
    public function optimizeDatabase()
    {
        try {
            $tables = DB::select('SHOW TABLES');
            $database = config('database.connections.mysql.database');

            foreach ($tables as $table) {
                $tableName = $table->{"Tables_in_$database"};
                DB::statement("OPTIMIZE TABLE `$tableName`");
            }

            return response()->json(['success' => true, 'message' => 'Base de données optimisée avec succès.']);
        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'optimisation: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Erreur lors de l\'optimisation.'], 500);
        }
    }

    /**
     * Générer un rapport système
     */
    public function generateReport()
    {
        try {
            $reportData = [
                'generated_at' => now()->format('Y-m-d H:i:s'),
                'system_info' => [
                    'php_version' => PHP_VERSION,
                    'laravel_version' => app()->version(),
                    'database' => config('database.default'),
                    'memory_limit' => ini_get('memory_limit'),
                    'max_execution_time' => ini_get('max_execution_time'),
                    'upload_max_filesize' => ini_get('upload_max_filesize'),
                ],
                'statistics' => [
                    'users_count' => \App\Models\User::count(),
                    'disk_free_space' => round(disk_free_space('/') / 1024 / 1024 / 1024, 2) . ' GB',
                    'last_backup' => $this->getLastBackupDate(),
                ],
                'settings' => [
                    'app_name' => $this->getSetting('app_name'),
                    'app_email' => $this->getSetting('app_email'),
                    'app_currency' => $this->getSetting('app_currency'),
                    'tax_rate' => $this->getSetting('tax_rate'),
                    'backup_enabled' => $this->getSetting('backup_enabled'),
                ]
            ];

            $filename = 'system_report_' . date('Y-m-d-H-i-s') . '.json';
            $filePath = storage_path('app/reports/' . $filename);

            // Créer le dossier reports s'il n'existe pas
            if (!File::exists(storage_path('app/reports'))) {
                File::makeDirectory(storage_path('app/reports'), 0755, true);
            }

            File::put($filePath, json_encode($reportData, JSON_PRETTY_PRINT));

            return response()->download($filePath)->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la génération du rapport: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Erreur lors de la génération du rapport.'], 500);
        }
    }

    /**
     * Vérifier les mises à jour
     */
    public function checkUpdates()
    {
        try {
            // Simulation de vérification des mises à jour
            // Dans un vrai projet, vous pourriez vérifier contre un serveur de mises à jour
            $currentVersion = '1.0.0';
            $latestVersion = '1.0.0';

            $upToDate = version_compare($currentVersion, $latestVersion) >= 0;

            return response()->json([
                'success' => true,
                'up_to_date' => $upToDate,
                'current_version' => $currentVersion,
                'latest_version' => $latestVersion,
                'message' => $upToDate ? 'Votre système est à jour.' : 'Une mise à jour est disponible.'
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la vérification des mises à jour: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Erreur lors de la vérification.'], 500);
        }
    }

    // ==================== MÉTHODES PRIVÉES ====================

    /**
     * Mettre à jour un paramètre
     */
    private function updateSetting($key, $value)
    {
        Setting::updateOrInsert(
            ['key' => $key, 'group' => 'general'],
            [
                'value' => $value,
                'updated_at' => now()
            ]
        );

        // Mettre à jour le cache si nécessaire
        Cache::forget('setting.' . $key);
    }

    /**
     * Créer une sauvegarde de la base de données
     */
    private function createDatabaseBackup($filePath)
    {
        try {
            // Méthode 1: Utiliser mysqldump si disponible
            if ($this->isMysqldumpAvailable()) {
                $this->createBackupWithMysqldump($filePath);
            } else {
                // Méthode 2: Utiliser Laravel pour exporter les données
                $this->createBackupWithLaravel($filePath);
            }
        } catch (\Exception $e) {
            Log::error('Erreur lors de la création de la sauvegarde: ' . $e->getMessage());
            throw new \Exception('Erreur lors de la création de la sauvegarde de la base de données: ' . $e->getMessage());
        }
    }

    /**
     * Vérifier si mysqldump est disponible
     */
    private function isMysqldumpAvailable()
    {
        $output = [];
        $returnVar = 0;
        exec('mysqldump --version 2>&1', $output, $returnVar);
        return $returnVar === 0;
    }

    /**
     * Créer une sauvegarde avec mysqldump
     */
    private function createBackupWithMysqldump($filePath)
    {
        $username = config('database.connections.mysql.username');
        $password = config('database.connections.mysql.password');
        $database = config('database.connections.mysql.database');
        $host = config('database.connections.mysql.host', 'localhost');
        $port = config('database.connections.mysql.port', 3306);

        // Construire la commande mysqldump
        $command = sprintf(
            'mysqldump --host=%s --port=%s --user=%s --password=%s --single-transaction --routines --triggers %s > %s',
            escapeshellarg($host),
            escapeshellarg($port),
            escapeshellarg($username),
            escapeshellarg($password),
            escapeshellarg($database),
            escapeshellarg($filePath)
        );

        $output = [];
        $returnVar = 0;
        exec($command . ' 2>&1', $output, $returnVar);

        if ($returnVar !== 0) {
            throw new \Exception('Erreur mysqldump: ' . implode("\n", $output));
        }
    }

    /**
     * Créer une sauvegarde avec Laravel (méthode alternative)
     */
    private function createBackupWithLaravel($filePath)
    {
        $tables = DB::select('SHOW TABLES');
        $database = config('database.connections.mysql.database');
        $sql = "-- Sauvegarde de la base de données $database\n";
        $sql .= "-- Généré le " . date('Y-m-d H:i:s') . "\n\n";
        $sql .= "SET FOREIGN_KEY_CHECKS=0;\n\n";

        foreach ($tables as $table) {
            $tableName = $table->{"Tables_in_$database"};

            // Structure de la table
            $createTable = DB::select("SHOW CREATE TABLE `$tableName`")[0];
            $sql .= "DROP TABLE IF EXISTS `$tableName`;\n";
            $sql .= $createTable->{'Create Table'} . ";\n\n";

            // Données de la table
            $rows = DB::table($tableName)->get();
            if ($rows->count() > 0) {
                $sql .= "INSERT INTO `$tableName` VALUES\n";
                $values = [];
                foreach ($rows as $row) {
                    $rowData = [];
                    foreach ((array)$row as $value) {
                        if (is_null($value)) {
                            $rowData[] = 'NULL';
                        } else {
                            $rowData[] = "'" . addslashes($value) . "'";
                        }
                    }
                    $values[] = '(' . implode(',', $rowData) . ')';
                }
                $sql .= implode(",\n", $values) . ";\n\n";
            }
        }

        $sql .= "SET FOREIGN_KEY_CHECKS=1;\n";

        if (File::put($filePath, $sql) === false) {
            throw new \Exception('Impossible d\'écrire le fichier de sauvegarde');
        }
    }

    /**
     * Restaurer la base de données
     */
    private function restoreDatabase($filePath)
    {
        try {
            // Vérifier que le fichier existe
            if (!File::exists($filePath)) {
                throw new \Exception('Fichier de sauvegarde non trouvé');
            }

            // Méthode 1: Utiliser mysql si disponible
            if ($this->isMysqlAvailable()) {
                $this->restoreWithMysql($filePath);
            } else {
                // Méthode 2: Utiliser Laravel pour exécuter le SQL
                $this->restoreWithLaravel($filePath);
            }
        } catch (\Exception $e) {
            Log::error('Erreur lors de la restauration: ' . $e->getMessage());
            throw new \Exception('Erreur lors de la restauration de la base de données: ' . $e->getMessage());
        }
    }

    /**
     * Vérifier si mysql est disponible
     */
    private function isMysqlAvailable()
    {
        $output = [];
        $returnVar = 0;
        exec('mysql --version 2>&1', $output, $returnVar);
        return $returnVar === 0;
    }

    /**
     * Restaurer avec mysql
     */
    private function restoreWithMysql($filePath)
    {
        $username = config('database.connections.mysql.username');
        $password = config('database.connections.mysql.password');
        $database = config('database.connections.mysql.database');
        $host = config('database.connections.mysql.host', 'localhost');
        $port = config('database.connections.mysql.port', 3306);

        $command = sprintf(
            'mysql --host=%s --port=%s --user=%s --password=%s %s < %s',
            escapeshellarg($host),
            escapeshellarg($port),
            escapeshellarg($username),
            escapeshellarg($password),
            escapeshellarg($database),
            escapeshellarg($filePath)
        );

        $output = [];
        $returnVar = 0;
        exec($command . ' 2>&1', $output, $returnVar);

        if ($returnVar !== 0) {
            throw new \Exception('Erreur mysql: ' . implode("\n", $output));
        }
    }

    /**
     * Restaurer avec Laravel (méthode alternative)
     */
    private function restoreWithLaravel($filePath)
    {
        $sql = File::get($filePath);

        // Diviser le SQL en requêtes individuelles
        $queries = array_filter(
            array_map('trim', explode(';', $sql)),
            function($query) {
                return !empty($query) && !preg_match('/^--/', $query);
            }
        );

        DB::beginTransaction();
        try {
            foreach ($queries as $query) {
                if (!empty(trim($query))) {
                    DB::statement($query);
                }
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Ajouter des fichiers à un ZIP
     */
    private function addFilesToZip($zip, $sourcePath, $zipPath = '')
    {
        if (!File::exists($sourcePath)) {
            return;
        }

        $files = File::allFiles($sourcePath);
        foreach ($files as $file) {
            $relativePath = $zipPath . $file->getRelativePathname();
            $zip->addFile($file->getRealPath(), $relativePath);
        }
    }

    /**
     * Enregistrer l'information de sauvegarde
     */
    private function logBackup($filename, $size)
    {
        // Vous pouvez enregistrer cela dans une table dédiée si nécessaire
        Log::info('Sauvegarde créée', [
            'filename' => $filename,
            'size' => $size,
            'created_at' => now()
        ]);
    }

    /**
     * Récupérer l'historique des sauvegardes
     */
    private function getBackupHistory()
    {
        $backupPath = storage_path('app/backups');
        $backups = [];

        if (File::exists($backupPath)) {
            $files = File::files($backupPath);

            foreach ($files as $file) {
                $backups[] = [
                    'filename' => $file->getFilename(),
                    'size' => $this->formatFileSize($file->getSize()),
                    'created_at' => Carbon::createFromTimestamp($file->getMTime()),
                    'type' => $file->getExtension() === 'zip' ? 'Complète' : 'Base de données'
                ];
            }

            // Trier par date de création (plus récent en premier)
            usort($backups, function($a, $b) {
                return $b['created_at']->timestamp - $a['created_at']->timestamp;
            });
        }

        return $backups;
    }

    /**
     * Formater la taille d'un fichier
     */
    private function formatFileSize($bytes)
    {
        if ($bytes === 0) return '0 B';

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $factor = floor(log($bytes, 1024));

        return sprintf('%.2f %s', $bytes / pow(1024, $factor), $units[$factor]);
    }

    /**
     * Récupérer la date de la dernière sauvegarde
     */
    private function getLastBackupDate()
    {
        $backups = $this->getBackupHistory();
        return !empty($backups) ? $backups[0]['created_at']->format('d/m/Y H:i') : 'Aucune sauvegarde';
    }
}
