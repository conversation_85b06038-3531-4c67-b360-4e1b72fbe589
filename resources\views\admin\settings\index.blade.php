@extends('layouts.admin_minimal')

@section('title', 'Paramètres')

@push('head')
<meta name="csrf-token" content="{{ csrf_token() }}">
@endpush

@push('styles')
<style>
    /* Variables CSS pour la cohérence */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
        --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        --card-hover-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        --border-radius: 15px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Styles généraux pour la page */
    .settings-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .settings-header {
        background: var(--primary-gradient);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
        box-shadow: var(--card-shadow);
        position: relative;
        overflow: hidden;
    }

    .settings-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .settings-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .settings-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0.5rem 0 0 0;
    }

    /* Navigation par onglets */
    .settings-nav {
        background: white;
        border-radius: var(--border-radius);
        padding: 1rem;
        margin-bottom: 2rem;
        box-shadow: var(--card-shadow);
    }

    .nav-pills .nav-link {
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        margin: 0 0.25rem;
        font-weight: 600;
        transition: var(--transition);
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
    }

    .nav-pills .nav-link:not(.active) {
        color: #6c757d;
        background: #f8f9fa;
    }

    .nav-pills .nav-link:not(.active):hover {
        background: #e9ecef;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .nav-pills .nav-link.active {
        background: var(--primary-gradient);
        color: white;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        transform: translateY(-2px);
    }

    .nav-pills .nav-link.active::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .nav-pills .nav-link.active:hover::before {
        left: 100%;
    }

    /* Cartes de contenu */
    .settings-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        border: none;
        overflow: hidden;
        transition: var(--transition);
        margin-bottom: 2rem;
    }

    .settings-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--card-hover-shadow);
    }

    .settings-card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border: none;
        position: relative;
    }

    .settings-card-header h5 {
        margin: 0;
        font-weight: 700;
        font-size: 1.25rem;
    }

    .settings-card-header .card-icon {
        position: absolute;
        right: 1.5rem;
        top: 50%;
        transform: translateY(-50%);
        font-size: 1.5rem;
        opacity: 0.8;
    }

    .settings-card-body {
        padding: 2rem;
    }

    /* Formulaires stylisés */
    .form-group {
        margin-bottom: 1.5rem;
        position: relative;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .form-control {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
        transition: var(--transition);
        background: #f8f9fa;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
        transform: translateY(-1px);
    }

    .form-control:hover:not(:focus) {
        border-color: #dee2e6;
        background: white;
    }

    /* Switches personnalisés */
    .form-switch .form-check-input {
        width: 3rem;
        height: 1.5rem;
        border-radius: 1rem;
        background-color: #dee2e6;
        border: none;
        transition: var(--transition);
    }

    .form-switch .form-check-input:checked {
        background: var(--primary-gradient);
        box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
    }

    .form-switch .form-check-input:focus {
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
    }

    /* Boutons stylisés */
    .btn-modern {
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        border: none;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-modern:hover::before {
        left: 100%;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .btn-primary-modern {
        background: var(--primary-gradient);
        color: white;
    }

    .btn-success-modern {
        background: var(--success-gradient);
        color: white;
    }

    .btn-warning-modern {
        background: var(--warning-gradient);
        color: white;
    }

    .btn-danger-modern {
        background: var(--danger-gradient);
        color: white;
    }

    /* Alertes personnalisées */
    .alert-modern {
        border: none;
        border-radius: var(--border-radius);
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        position: relative;
        overflow: hidden;
    }

    .alert-modern::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 4px;
        height: 100%;
        background: currentColor;
    }

    .alert-success-modern {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
        border-left: 4px solid #28a745;
    }

    .alert-danger-modern {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
        border-left: 4px solid #dc3545;
    }

    /* Animations d'entrée */
    .fade-in {
        animation: fadeIn 0.5s ease-in;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .slide-in {
        animation: slideIn 0.6s ease-out;
    }

    @keyframes slideIn {
        from { opacity: 0; transform: translateX(-30px); }
        to { opacity: 1; transform: translateX(0); }
    }

    /* Zone de fichier drag & drop */
    .file-drop-zone {
        border: 2px dashed #dee2e6;
        border-radius: var(--border-radius);
        padding: 2rem;
        text-align: center;
        transition: var(--transition);
        background: #f8f9fa;
        cursor: pointer;
    }

    .file-drop-zone:hover,
    .file-drop-zone.dragover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.05);
        transform: scale(1.02);
    }

    .file-drop-zone i {
        font-size: 3rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .settings-header {
            padding: 1.5rem;
        }

        .settings-title {
            font-size: 2rem;
        }

        .nav-pills .nav-link {
            margin-bottom: 0.5rem;
            text-align: center;
        }

        .settings-card-body {
            padding: 1.5rem;
        }
    }

    /* Indicateurs de statut */
    .status-indicator {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 0.5rem;
        animation: pulse 2s infinite;
    }

    .status-online {
        background: #28a745;
    }

    .status-offline {
        background: #dc3545;
    }

    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
        100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
    }
</style>
@endpush

@section('content')
<div class="settings-container">
    <div class="container-fluid">
        <!-- En-tête moderne -->
        <div class="settings-header fade-in">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="settings-title">
                        <i class="fas fa-cogs me-3"></i>
                        Paramètres Système
                    </h1>
                    <p class="settings-subtitle">
                        Configurez et gérez les paramètres de votre application GRADIS
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex align-items-center justify-content-end">
                        <span class="status-indicator status-online"></span>
                        <span class="text-white-50">Système en ligne</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Messages d'alerte modernes -->
        @if(session('success'))
            <div class="alert alert-success-modern alert-dismissible fade show slide-in" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger-modern alert-dismissible fade show slide-in" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        <!-- Navigation par onglets -->
        <div class="settings-nav fade-in">
            <ul class="nav nav-pills justify-content-center" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="general-tab" data-bs-toggle="pill" data-bs-target="#general" type="button" role="tab">
                        <i class="fas fa-sliders-h me-2"></i>
                        Paramètres Généraux
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="backup-tab" data-bs-toggle="pill" data-bs-target="#backup" type="button" role="tab">
                        <i class="fas fa-shield-alt me-2"></i>
                        Sauvegarde & Sécurité
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="system-tab" data-bs-toggle="pill" data-bs-target="#system" type="button" role="tab">
                        <i class="fas fa-server me-2"></i>
                        Informations Système
                    </button>
                </li>
            </ul>
        </div>

        <!-- Contenu des onglets -->
        <div class="tab-content" id="settingsTabContent">
            <!-- Onglet Paramètres Généraux -->
            <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="settings-card slide-in">
                            <div class="settings-card-header">
                                <h5>Configuration de l'Application</h5>
                                <i class="fas fa-cog card-icon"></i>
                            </div>
                            <div class="settings-card-body">
                                <form action="{{ route('admin.settings.update') }}" method="POST" id="generalSettingsForm">
                                    @csrf
                                    @method('PUT')

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="app_name" class="form-label">
                                                    <i class="fas fa-tag me-2 text-primary"></i>
                                                    Nom de l'application
                                                </label>
                                                <input type="text"
                                                       class="form-control @error('app_name') is-invalid @enderror"
                                                       id="app_name"
                                                       name="app_name"
                                                       value="{{ old('app_name', $settings['app_name']) }}"
                                                       placeholder="Ex: GRADIS Pro"
                                                       required>
                                                @error('app_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="app_email" class="form-label">
                                                    <i class="fas fa-envelope me-2 text-primary"></i>
                                                    Email de l'application
                                                </label>
                                                <input type="email"
                                                       class="form-control @error('app_email') is-invalid @enderror"
                                                       id="app_email"
                                                       name="app_email"
                                                       value="{{ old('app_email', $settings['app_email']) }}"
                                                       placeholder="<EMAIL>"
                                                       required>
                                                @error('app_email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="app_currency" class="form-label">
                                                    <i class="fas fa-coins me-2 text-primary"></i>
                                                    Devise par défaut
                                                </label>
                                                <select class="form-control @error('app_currency') is-invalid @enderror"
                                                        id="app_currency"
                                                        name="app_currency"
                                                        required>
                                                    <option value="FCFA" {{ old('app_currency', $settings['app_currency']) == 'FCFA' ? 'selected' : '' }}>FCFA (Franc CFA)</option>
                                                    <option value="EUR" {{ old('app_currency', $settings['app_currency']) == 'EUR' ? 'selected' : '' }}>EUR (Euro)</option>
                                                    <option value="USD" {{ old('app_currency', $settings['app_currency']) == 'USD' ? 'selected' : '' }}>USD (Dollar US)</option>
                                                    <option value="GBP" {{ old('app_currency', $settings['app_currency']) == 'GBP' ? 'selected' : '' }}>GBP (Livre Sterling)</option>
                                                </select>
                                                @error('app_currency')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="tax_rate" class="form-label">
                                                    <i class="fas fa-percentage me-2 text-primary"></i>
                                                    Taux de TVA (%)
                                                </label>
                                                <div class="input-group">
                                                    <input type="number"
                                                           class="form-control @error('tax_rate') is-invalid @enderror"
                                                           id="tax_rate"
                                                           name="tax_rate"
                                                           value="{{ old('tax_rate', $settings['tax_rate']) }}"
                                                           min="0"
                                                           max="100"
                                                           step="0.01"
                                                           placeholder="18.00"
                                                           required>
                                                    <span class="input-group-text">%</span>
                                                </div>
                                                @error('tax_rate')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-12">
                                            <div class="form-group">
                                                <div class="d-flex align-items-center justify-content-between p-3 bg-light rounded">
                                                    <div>
                                                        <label class="form-label mb-1">
                                                            <i class="fas fa-robot me-2 text-primary"></i>
                                                            Sauvegardes automatiques
                                                        </label>
                                                        <p class="text-muted small mb-0">
                                                            Active la création automatique de sauvegardes quotidiennes
                                                        </p>
                                                    </div>
                                                    <div class="form-check form-switch">
                                                        <input type="checkbox"
                                                               class="form-check-input @error('backup_enabled') is-invalid @enderror"
                                                               id="backup_enabled"
                                                               name="backup_enabled"
                                                               value="1"
                                                               {{ old('backup_enabled', $settings['backup_enabled']) ? 'checked' : '' }}>
                                                    </div>
                                                </div>
                                                @error('backup_enabled')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-end">
                                        <button type="button" class="btn btn-secondary me-3" onclick="resetForm()">
                                            <i class="fas fa-undo me-2"></i>
                                            Réinitialiser
                                        </button>
                                        <button type="submit" class="btn btn-modern btn-primary-modern">
                                            <i class="fas fa-save me-2"></i>
                                            Enregistrer les modifications
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="settings-card slide-in">
                            <div class="settings-card-header">
                                <h5>Aide & Conseils</h5>
                                <i class="fas fa-lightbulb card-icon"></i>
                            </div>
                            <div class="settings-card-body">
                                <div class="mb-3">
                                    <h6 class="text-primary">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Nom de l'application
                                    </h6>
                                    <p class="small text-muted">
                                        Ce nom apparaîtra dans l'interface utilisateur et les emails envoyés par le système.
                                    </p>
                                </div>

                                <div class="mb-3">
                                    <h6 class="text-primary">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Email système
                                    </h6>
                                    <p class="small text-muted">
                                        Adresse email utilisée pour l'envoi des notifications et communications automatiques.
                                    </p>
                                </div>

                                <div class="mb-3">
                                    <h6 class="text-primary">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Devise
                                    </h6>
                                    <p class="small text-muted">
                                        Devise utilisée par défaut pour tous les calculs financiers et l'affichage des montants.
                                    </p>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    <strong>Important :</strong> Certains changements peuvent nécessiter une reconnexion des utilisateurs.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Onglet Sauvegarde & Sécurité -->
            <div class="tab-pane fade" id="backup" role="tabpanel" aria-labelledby="backup-tab">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="settings-card slide-in">
                            <div class="settings-card-header">
                                <h5>Créer une Sauvegarde</h5>
                                <i class="fas fa-download card-icon"></i>
                            </div>
                            <div class="settings-card-body">
                                <div class="text-center mb-4">
                                    <div class="backup-icon mb-3">
                                        <i class="fas fa-database" style="font-size: 3rem; color: #28a745;"></i>
                                    </div>
                                    <h6>Sauvegarde complète du système</h6>
                                    <p class="text-muted">
                                        Créez une sauvegarde complète de votre base de données pour sécuriser vos données.
                                    </p>
                                </div>

                                <form action="{{ route('admin.settings.backup') }}" method="POST" id="backupForm">
                                    @csrf
                                    <div class="backup-options mb-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="include_files" name="include_files" checked>
                                            <label class="form-check-label" for="include_files">
                                                <i class="fas fa-folder me-2"></i>
                                                Inclure les fichiers uploadés
                                            </label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="include_logs" name="include_logs">
                                            <label class="form-check-label" for="include_logs">
                                                <i class="fas fa-file-alt me-2"></i>
                                                Inclure les logs système
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="compress_backup" name="compress_backup" checked>
                                            <label class="form-check-label" for="compress_backup">
                                                <i class="fas fa-compress me-2"></i>
                                                Compresser la sauvegarde
                                            </label>
                                        </div>
                                    </div>

                                    <button type="submit" class="btn btn-modern btn-success-modern w-100" id="createBackupBtn">
                                        <i class="fas fa-download me-2"></i>
                                        Créer une sauvegarde maintenant
                                    </button>
                                </form>

                                <div class="backup-info mt-4 p-3 bg-light rounded">
                                    <h6 class="text-success mb-2">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Informations
                                    </h6>
                                    <ul class="list-unstyled small mb-0">
                                        <li><i class="fas fa-check text-success me-2"></i>Dernière sauvegarde : <span id="lastBackupDate">{{ date('d/m/Y H:i') }}</span></li>
                                        <li><i class="fas fa-check text-success me-2"></i>Taille estimée : <span id="estimatedSize">~15 MB</span></li>
                                        <li><i class="fas fa-check text-success me-2"></i>Durée estimée : <span id="estimatedTime">2-3 minutes</span></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="settings-card slide-in">
                            <div class="settings-card-header">
                                <h5>Restaurer une Sauvegarde</h5>
                                <i class="fas fa-upload card-icon"></i>
                            </div>
                            <div class="settings-card-body">
                                <div class="text-center mb-4">
                                    <div class="restore-icon mb-3">
                                        <i class="fas fa-history" style="font-size: 3rem; color: #ffc107;"></i>
                                    </div>
                                    <h6>Restauration du système</h6>
                                    <p class="text-muted">
                                        Restaurez votre système à partir d'une sauvegarde précédente.
                                    </p>
                                </div>

                                <form action="{{ route('admin.settings.restore') }}" method="POST" enctype="multipart/form-data" id="restoreForm">
                                    @csrf
                                    <div class="file-drop-zone mb-4" id="fileDropZone">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <h6 class="mt-2">Glissez votre fichier ici</h6>
                                        <p class="text-muted small">ou cliquez pour sélectionner</p>
                                        <input type="file"
                                               class="form-control d-none @error('backup_file') is-invalid @enderror"
                                               id="backup_file"
                                               name="backup_file"
                                               accept=".sql,.zip"
                                               required>
                                    </div>

                                    <div id="fileInfo" class="file-info mb-3" style="display: none;">
                                        <div class="d-flex align-items-center p-3 bg-light rounded">
                                            <i class="fas fa-file-archive text-primary me-3"></i>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1" id="fileName"></h6>
                                                <small class="text-muted" id="fileSize"></small>
                                            </div>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile()">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>

                                    @error('backup_file')
                                        <div class="alert alert-danger">{{ $message }}</div>
                                    @enderror

                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>Attention :</strong> Cette opération remplacera toutes les données actuelles. Assurez-vous d'avoir une sauvegarde récente.
                                    </div>

                                    <button type="submit" class="btn btn-modern btn-warning-modern w-100" id="restoreBtn" disabled>
                                        <i class="fas fa-upload me-2"></i>
                                        Restaurer la sauvegarde
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="settings-card slide-in">
                            <div class="settings-card-header">
                                <h5>Historique des Sauvegardes</h5>
                                <i class="fas fa-history card-icon"></i>
                            </div>
                            <div class="settings-card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th><i class="fas fa-calendar me-2"></i>Date</th>
                                                <th><i class="fas fa-file me-2"></i>Nom du fichier</th>
                                                <th><i class="fas fa-weight me-2"></i>Taille</th>
                                                <th><i class="fas fa-check-circle me-2"></i>Statut</th>
                                                <th><i class="fas fa-cogs me-2"></i>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($backups as $backup)
                                            <tr>
                                                <td>{{ $backup['created_at']->format('d/m/Y H:i') }}</td>
                                                <td>{{ $backup['filename'] }}</td>
                                                <td><span class="badge bg-info">{{ $backup['size'] }}</span></td>
                                                <td><span class="badge bg-success">{{ $backup['type'] }}</span></td>
                                                <td>
                                                    <a href="{{ route('admin.settings.backup.download', $backup['filename']) }}"
                                                       class="btn btn-sm btn-outline-primary me-1"
                                                       title="Télécharger">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-danger"
                                                            title="Supprimer"
                                                            onclick="deleteBackup('{{ $backup['filename'] }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            @empty
                                            <tr>
                                                <td colspan="5" class="text-center text-muted">
                                                    <i class="fas fa-inbox fa-2x mb-2"></i>
                                                    <p>Aucune sauvegarde disponible</p>
                                                </td>
                                            </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Onglet Informations Système -->
            <div class="tab-pane fade" id="system" role="tabpanel" aria-labelledby="system-tab">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="settings-card slide-in">
                            <div class="settings-card-header">
                                <h5>Informations Serveur</h5>
                                <i class="fas fa-server card-icon"></i>
                            </div>
                            <div class="settings-card-body">
                                <div class="system-info">
                                    <div class="info-item d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                                        <div>
                                            <i class="fas fa-code text-primary me-2"></i>
                                            <strong>Version PHP</strong>
                                        </div>
                                        <span class="badge bg-success">{{ PHP_VERSION }}</span>
                                    </div>

                                    <div class="info-item d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                                        <div>
                                            <i class="fab fa-laravel text-danger me-2"></i>
                                            <strong>Version Laravel</strong>
                                        </div>
                                        <span class="badge bg-danger">{{ app()->version() }}</span>
                                    </div>

                                    <div class="info-item d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                                        <div>
                                            <i class="fas fa-database text-info me-2"></i>
                                            <strong>Base de données</strong>
                                        </div>
                                        <span class="badge bg-info">{{ config('database.default') }}</span>
                                    </div>

                                    <div class="info-item d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                                        <div>
                                            <i class="fas fa-memory text-warning me-2"></i>
                                            <strong>Mémoire PHP</strong>
                                        </div>
                                        <span class="badge bg-warning">{{ ini_get('memory_limit') }}</span>
                                    </div>

                                    <div class="info-item d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                                        <div>
                                            <i class="fas fa-clock text-secondary me-2"></i>
                                            <strong>Temps d'exécution max</strong>
                                        </div>
                                        <span class="badge bg-secondary">{{ ini_get('max_execution_time') }}s</span>
                                    </div>

                                    <div class="info-item d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                                        <div>
                                            <i class="fas fa-upload text-primary me-2"></i>
                                            <strong>Taille max upload</strong>
                                        </div>
                                        <span class="badge bg-primary">{{ ini_get('upload_max_filesize') }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="settings-card slide-in">
                            <div class="settings-card-header">
                                <h5>Statistiques Système</h5>
                                <i class="fas fa-chart-line card-icon"></i>
                            </div>
                            <div class="settings-card-body">
                                <div class="stats-grid">
                                    <div class="stat-card mb-3 p-3 bg-gradient-primary text-white rounded">
                                        <div class="d-flex align-items-center">
                                            <div class="stat-icon me-3">
                                                <i class="fas fa-users fa-2x"></i>
                                            </div>
                                            <div>
                                                <h3 class="mb-0">{{ \App\Models\User::count() }}</h3>
                                                <p class="mb-0">Utilisateurs actifs</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="stat-card mb-3 p-3 bg-gradient-success text-white rounded">
                                        <div class="d-flex align-items-center">
                                            <div class="stat-icon me-3">
                                                <i class="fas fa-file-invoice fa-2x"></i>
                                            </div>
                                            <div>
                                                <h3 class="mb-0">0</h3>
                                                <p class="mb-0">Factures générées</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="stat-card mb-3 p-3 bg-gradient-warning text-white rounded">
                                        <div class="d-flex align-items-center">
                                            <div class="stat-icon me-3">
                                                <i class="fas fa-hdd fa-2x"></i>
                                            </div>
                                            <div>
                                                <h3 class="mb-0">{{ round(disk_free_space('/') / 1024 / 1024 / 1024, 1) }} GB</h3>
                                                <p class="mb-0">Espace disque libre</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="stat-card mb-3 p-3 bg-gradient-info text-white rounded">
                                        <div class="d-flex align-items-center">
                                            <div class="stat-icon me-3">
                                                <i class="fas fa-clock fa-2x"></i>
                                            </div>
                                            <div>
                                                <h3 class="mb-0">{{ \Carbon\Carbon::now()->diffForHumans(\Carbon\Carbon::createFromTimestamp(filemtime(base_path()))) }}</h3>
                                                <p class="mb-0">Dernière mise à jour</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="settings-card slide-in mt-4">
                            <div class="settings-card-header">
                                <h5>Actions Système</h5>
                                <i class="fas fa-tools card-icon"></i>
                            </div>
                            <div class="settings-card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary" onclick="performSystemAction('cache')">
                                        <i class="fas fa-broom me-2"></i>
                                        Vider le cache
                                    </button>
                                    <button class="btn btn-outline-info" onclick="performSystemAction('database')">
                                        <i class="fas fa-database me-2"></i>
                                        Optimiser la base de données
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="performSystemAction('report')">
                                        <i class="fas fa-file-pdf me-2"></i>
                                        Générer un rapport système
                                    </button>
                                    <button class="btn btn-outline-success" onclick="performSystemAction('updates')">
                                        <i class="fas fa-sync-alt me-2"></i>
                                        Vérifier les mises à jour
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de progression -->
<div class="modal fade" id="progressModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">
                    <i class="fas fa-cog fa-spin me-2"></i>
                    Opération en cours...
                </h5>
            </div>
            <div class="modal-body text-center">
                <div class="progress mb-3" style="height: 20px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar"
                         style="width: 0%"
                         id="progressBar">
                    </div>
                </div>
                <p id="progressText">Initialisation...</p>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation des cartes au scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    document.querySelectorAll('.settings-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s ease';
        observer.observe(card);
    });

    // Gestion du drag & drop pour les fichiers
    const fileDropZone = document.getElementById('fileDropZone');
    const fileInput = document.getElementById('backup_file');
    const fileInfo = document.getElementById('fileInfo');
    const restoreBtn = document.getElementById('restoreBtn');

    if (fileDropZone && fileInput) {
        fileDropZone.addEventListener('click', () => fileInput.click());

        fileDropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileDropZone.classList.add('dragover');
        });

        fileDropZone.addEventListener('dragleave', () => {
            fileDropZone.classList.remove('dragover');
        });

        fileDropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            fileDropZone.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });
    }

    function handleFileSelect(file) {
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = formatFileSize(file.size);
        fileInfo.style.display = 'block';
        fileDropZone.style.display = 'none';
        restoreBtn.disabled = false;
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Gestion des formulaires avec progression
    const forms = ['generalSettingsForm', 'backupForm', 'restoreForm'];
    forms.forEach(formId => {
        const form = document.getElementById(formId);
        if (form) {
            form.addEventListener('submit', function(e) {
                if (formId === 'restoreForm') {
                    e.preventDefault();
                    showConfirmDialog('Êtes-vous sûr de vouloir restaurer cette sauvegarde ?', () => {
                        showProgress('Restauration en cours...');
                        form.submit();
                    });
                } else if (formId === 'backupForm') {
                    e.preventDefault();
                    showProgress('Création de la sauvegarde...');
                    setTimeout(() => form.submit(), 1000);
                }
            });
        }
    });
});

// Fonctions utilitaires
function resetForm() {
    document.getElementById('generalSettingsForm').reset();
    Swal.fire({
        icon: 'info',
        title: 'Formulaire réinitialisé',
        text: 'Les valeurs ont été remises à leur état initial.',
        timer: 2000,
        showConfirmButton: false
    });
}

function removeFile() {
    document.getElementById('backup_file').value = '';
    document.getElementById('fileInfo').style.display = 'none';
    document.getElementById('fileDropZone').style.display = 'block';
    document.getElementById('restoreBtn').disabled = true;
}

function showProgress(message) {
    document.getElementById('progressText').textContent = message;
    const modal = new bootstrap.Modal(document.getElementById('progressModal'));
    modal.show();

    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;
        document.getElementById('progressBar').style.width = progress + '%';
    }, 500);

    setTimeout(() => {
        clearInterval(interval);
        document.getElementById('progressBar').style.width = '100%';
        setTimeout(() => modal.hide(), 1000);
    }, 3000);
}

function showConfirmDialog(message, callback) {
    Swal.fire({
        title: 'Confirmation',
        text: message,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Oui, continuer',
        cancelButtonText: 'Annuler'
    }).then((result) => {
        if (result.isConfirmed) {
            callback();
        }
    });
}

// Actions système
function performSystemAction(action) {
    const actions = {
        cache: {
            url: '{{ route("admin.settings.cache.clear") }}',
            method: 'POST',
            message: 'Vidage du cache...',
            successTitle: 'Cache vidé',
            successText: 'Le cache a été vidé avec succès.'
        },
        database: {
            url: '{{ route("admin.settings.database.optimize") }}',
            method: 'POST',
            message: 'Optimisation de la base de données...',
            successTitle: 'Base de données optimisée',
            successText: 'L\'optimisation a été effectuée avec succès.'
        },
        report: {
            url: '{{ route("admin.settings.report.generate") }}',
            method: 'GET',
            message: 'Génération du rapport...',
            successTitle: 'Rapport généré',
            successText: 'Le rapport système a été généré et téléchargé.',
            download: true
        },
        updates: {
            url: '{{ route("admin.settings.updates.check") }}',
            method: 'GET',
            message: 'Vérification des mises à jour...',
            successTitle: 'Vérification terminée',
            successText: 'Vérification des mises à jour effectuée.'
        }
    };

    const config = actions[action];
    if (!config) return;

    showProgress(config.message);

    const options = {
        method: config.method,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    };

    fetch(config.url, options)
        .then(response => {
            if (config.download && response.ok) {
                // Pour les téléchargements
                return response.blob().then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'system_report.json';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    return { success: true, message: config.successText };
                });
            }
            return response.json();
        })
        .then(data => {
            hideProgress();
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: config.successTitle,
                    text: data.message || config.successText,
                    timer: 3000,
                    showConfirmButton: false
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Erreur',
                    text: data.message || 'Une erreur est survenue.',
                    showConfirmButton: true
                });
            }
        })
        .catch(error => {
            hideProgress();
            console.error('Erreur:', error);
            Swal.fire({
                icon: 'error',
                title: 'Erreur',
                text: 'Une erreur est survenue lors de l\'opération.',
                showConfirmButton: true
            });
        });
}

// Supprimer une sauvegarde
function deleteBackup(filename) {
    Swal.fire({
        title: 'Confirmer la suppression',
        text: `Êtes-vous sûr de vouloir supprimer la sauvegarde "${filename}" ?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Oui, supprimer',
        cancelButtonText: 'Annuler'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`{{ route('admin.settings.backup.delete', '') }}/${filename}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Supprimé',
                        text: data.message,
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload(); // Recharger la page pour mettre à jour la liste
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Erreur',
                        text: data.message,
                        showConfirmButton: true
                    });
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Erreur',
                    text: 'Une erreur est survenue lors de la suppression.',
                    showConfirmButton: true
                });
            });
        }
    });
}

// Fonction pour masquer la barre de progression
function hideProgress() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('progressModal'));
    if (modal) {
        modal.hide();
    }
}
</script>
@endpush
