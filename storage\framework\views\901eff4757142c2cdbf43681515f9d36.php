<?php $__env->startSection('title', 'Ajouter un utilisateur'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4">
    <!-- Header moderne avec breadcrumb -->
    <div class="modern-header-wrapper">
        <div class="modern-header-background"></div>
        <div class="modern-header-content">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-2">
                            <li class="breadcrumb-item">
                                <a href="<?php echo e(route('admin.users.index')); ?>" class="text-white text-decoration-none">
                                    <i class="fas fa-users me-1"></i> Utilisateurs
                                </a>
                            </li>
                            <li class="breadcrumb-item active text-white-50" aria-current="page">Nouvel utilisateur</li>
                        </ol>
                    </nav>
                    <h1 class="h2 mb-0 text-white fw-bold">
                        <i class="fas fa-user-plus text-warning me-2"></i>
                        Créer un Nouvel Utilisateur
                    </h1>
                    <p class="text-white-50 mb-0">Ajoutez un nouveau membre à votre équipe</p>
                </div>
                <div>
                    <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-light btn-lg shadow-sm">
                        <i class="fas fa-arrow-left me-2"></i> Retour à la liste
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-xl-8 col-lg-10">
            <!-- Formulaire principal -->
            <div class="card modern-card shadow-lg border-0">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-white bg-opacity-20 me-3">
                            <i class="fas fa-user-plus text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-0 fw-bold">Informations de l'utilisateur</h5>
                            <small class="text-white-50">Remplissez tous les champs requis</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <form action="<?php echo e(route('admin.users.store')); ?>" method="POST" id="userForm" novalidate>
                        <?php echo csrf_field(); ?>

                        <!-- Section Informations personnelles -->
                        <div class="form-section mb-4">
                            <div class="section-header mb-3">
                                <h6 class="text-primary fw-bold mb-0">
                                    <i class="fas fa-user me-2"></i>Informations personnelles
                                </h6>
                                <hr class="mt-2 mb-0">
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label fw-semibold">
                                        <i class="fas fa-user text-primary me-1"></i>Nom complet
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-user text-muted"></i>
                                        </span>
                                        <input type="text"
                                               class="form-control border-start-0 <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="name"
                                               name="name"
                                               value="<?php echo e(old('name')); ?>"
                                               placeholder="Entrez le nom complet"
                                               required>
                                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label fw-semibold">
                                        <i class="fas fa-envelope text-primary me-1"></i>Adresse email
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-envelope text-muted"></i>
                                        </span>
                                        <input type="email"
                                               class="form-control border-start-0 <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="email"
                                               name="email"
                                               value="<?php echo e(old('email')); ?>"
                                               placeholder="<EMAIL>"
                                               required>
                                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Section Sécurité -->
                        <div class="form-section mb-4">
                            <div class="section-header mb-3">
                                <h6 class="text-success fw-bold mb-0">
                                    <i class="fas fa-shield-alt me-2"></i>Sécurité et authentification
                                </h6>
                                <hr class="mt-2 mb-0">
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label fw-semibold">
                                        <i class="fas fa-lock text-success me-1"></i>Mot de passe
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-lock text-muted"></i>
                                        </span>
                                        <input type="password"
                                               class="form-control border-start-0 border-end-0 <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="password"
                                               name="password"
                                               placeholder="Minimum 8 caractères"
                                               required>
                                        <button class="btn btn-outline-secondary border-start-0" type="button" id="togglePassword">
                                            <i class="fas fa-eye" id="passwordIcon"></i>
                                        </button>
                                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="form-text">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Le mot de passe doit contenir au moins 8 caractères
                                        </small>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="password_confirmation" class="form-label fw-semibold">
                                        <i class="fas fa-check-circle text-success me-1"></i>Confirmer le mot de passe
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-check-circle text-muted"></i>
                                        </span>
                                        <input type="password"
                                               class="form-control border-start-0 border-end-0"
                                               id="password_confirmation"
                                               name="password_confirmation"
                                               placeholder="Répétez le mot de passe"
                                               required>
                                        <button class="btn btn-outline-secondary border-start-0" type="button" id="togglePasswordConfirm">
                                            <i class="fas fa-eye" id="passwordConfirmIcon"></i>
                                        </button>
                                    </div>
                                    <div id="passwordMatch" class="form-text"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Section Rôles et permissions -->
                        <div class="form-section mb-4">
                            <div class="section-header mb-3">
                                <h6 class="text-warning fw-bold mb-0">
                                    <i class="fas fa-user-tag me-2"></i>Rôles et permissions
                                </h6>
                                <hr class="mt-2 mb-0">
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-semibold">
                                    <i class="fas fa-users-cog text-warning me-1"></i>Sélectionner les rôles
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="roles-container">
                                    <div class="row g-3">
                                        <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="col-md-6 col-lg-4">
                                                <div class="role-card">
                                                    <input class="form-check-input role-checkbox"
                                                           type="checkbox"
                                                           name="roles[]"
                                                           value="<?php echo e($role->name); ?>"
                                                           id="role_<?php echo e($role->id); ?>"
                                                           <?php echo e(in_array($role->name, old('roles', [])) ? 'checked' : ''); ?>>
                                                    <label class="role-label" for="role_<?php echo e($role->id); ?>">
                                                        <div class="role-icon">
                                                            <i class="fas fa-<?php echo e($role->name === 'admin' ? 'crown' : ($role->name === 'manager' ? 'user-tie' : 'user')); ?>"></i>
                                                        </div>
                                                        <div class="role-info">
                                                            <div class="role-name"><?php echo e(ucfirst($role->name)); ?></div>
                                                            <div class="role-description">
                                                                <?php switch($role->name):
                                                                    case ('admin'): ?>
                                                                        Accès complet au système
                                                                        <?php break; ?>
                                                                    <?php case ('manager'): ?>
                                                                        Gestion des équipes
                                                                        <?php break; ?>
                                                                    <?php case ('user'): ?>
                                                                        Utilisateur standard
                                                                        <?php break; ?>
                                                                    <?php default: ?>
                                                                        Rôle <?php echo e($role->name); ?>

                                                                <?php endswitch; ?>
                                                            </div>
                                                        </div>
                                                        <div class="role-check">
                                                            <i class="fas fa-check"></i>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                                <?php $__errorArgs = ['roles'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger mt-2">
                                        <i class="fas fa-exclamation-triangle me-1"></i><?php echo e($message); ?>

                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="form-actions">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-outline-secondary btn-lg w-100" id="previewBtn">
                                        <i class="fas fa-eye me-2"></i>Aperçu
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-primary btn-lg w-100" id="submitBtn">
                                        <span class="btn-text">
                                            <i class="fas fa-user-plus me-2"></i>Créer l'utilisateur
                                        </span>
                                        <span class="btn-loading d-none">
                                            <i class="fas fa-spinner fa-spin me-2"></i>Création en cours...
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Carte d'aperçu (masquée par défaut) -->
            <div class="card modern-card shadow-lg border-0 mt-4 d-none" id="previewCard">
                <div class="card-header bg-gradient-info text-white">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-white bg-opacity-20 me-3">
                            <i class="fas fa-eye text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-0 fw-bold">Aperçu de l'utilisateur</h5>
                            <small class="text-white-50">Vérifiez les informations avant la création</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="user-preview">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="avatar-preview me-3">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0" id="previewName">Nom de l'utilisateur</h6>
                                        <small class="text-muted" id="previewEmail"><EMAIL></small>
                                    </div>
                                </div>
                                <div class="roles-preview">
                                    <h6 class="text-muted mb-2">Rôles assignés :</h6>
                                    <div id="previewRoles" class="d-flex flex-wrap gap-2">
                                        <!-- Les rôles seront ajoutés dynamiquement -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="preview-stats">
                                <div class="stat-item">
                                    <div class="stat-icon bg-primary">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <div class="stat-info">
                                        <div class="stat-label">Sécurité</div>
                                        <div class="stat-value">Mot de passe défini</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light">
    <div class="container text-center">
        <span class="text-muted">© <?php echo e(date('Y')); ?> GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>
<?php $__env->startPush('styles'); ?>
<style>
/* ===== VARIABLES CSS ===== */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --light-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

/* ===== HEADER MODERNE ===== */
.modern-header-wrapper {
    position: relative;
    margin: -1.5rem -1.5rem 2rem -1.5rem;
    overflow: hidden;
    border-radius: 0 0 20px 20px;
}

.modern-header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0.9;
}

.modern-header-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

.modern-header-content {
    position: relative;
    z-index: 2;
    padding: 2rem;
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: rgba(255, 255, 255, 0.7);
    font-weight: bold;
}

/* ===== CARTES MODERNES ===== */
.modern-card {
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
    animation: slideInUp 0.6s ease-out;
}

.modern-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
}

.card-header.bg-gradient-primary {
    background: var(--primary-gradient) !important;
    border: none;
    padding: 1.5rem;
}

.card-header.bg-gradient-info {
    background: var(--info-gradient) !important;
    border: none;
    padding: 1.5rem;
}

.icon-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

/* ===== SECTIONS DE FORMULAIRE ===== */
.form-section {
    position: relative;
    padding: 1.5rem;
    background: linear-gradient(145deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 15px;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.section-header h6 {
    position: relative;
    display: inline-block;
    padding: 0.5rem 1rem;
    background: white;
    border-radius: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* ===== CHAMPS DE SAISIE AMÉLIORÉS ===== */
.input-group {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.input-group:focus-within {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.2);
}

.input-group-text {
    border: none;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
}

.form-control {
    border: none;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    box-shadow: none;
    border-color: transparent;
    background: rgba(102, 126, 234, 0.05);
}

/* ===== CARTES DE RÔLES ===== */
.roles-container {
    padding: 1rem;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 15px;
    border: 2px dashed rgba(102, 126, 234, 0.2);
}

.role-card {
    position: relative;
    transition: all 0.3s ease;
}

.role-checkbox {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.role-label {
    display: block;
    padding: 1.5rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.role-label::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.5s ease;
}

.role-label:hover::before {
    left: 100%;
}

.role-label:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.role-checkbox:checked + .role-label {
    background: var(--primary-gradient);
    color: white;
    border-color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.role-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.role-checkbox:checked + .role-label .role-icon {
    opacity: 1;
    transform: scale(1.1);
}

.role-name {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.role-description {
    font-size: 0.85rem;
    opacity: 0.8;
}

.role-check {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 25px;
    height: 25px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
}

.role-checkbox:checked + .role-label .role-check {
    opacity: 1;
    transform: scale(1);
}

/* ===== BOUTONS D'ACTION ===== */
.form-actions {
    padding: 2rem;
    background: linear-gradient(145deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 15px;
    margin-top: 2rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--primary-gradient);
    border: none;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-outline-secondary {
    border: 2px solid #6c757d;
    color: #6c757d;
    background: white;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
}

/* ===== APERÇU UTILISATEUR ===== */
.avatar-preview {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.preview-stats .stat-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
}

.stat-icon.bg-primary {
    background: var(--primary-gradient);
}

/* ===== ANIMATIONS ===== */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.btn-loading {
    animation: pulse 1.5s infinite;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .modern-header-content {
        padding: 1.5rem;
    }

    .form-section {
        padding: 1rem;
    }

    .role-label {
        padding: 1rem;
    }

    .form-actions {
        padding: 1.5rem;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // ===== ÉLÉMENTS DU DOM =====
    const form = document.getElementById('userForm');
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const passwordConfirmInput = document.getElementById('password_confirmation');
    const togglePasswordBtn = document.getElementById('togglePassword');
    const togglePasswordConfirmBtn = document.getElementById('togglePasswordConfirm');
    const passwordIcon = document.getElementById('passwordIcon');
    const passwordConfirmIcon = document.getElementById('passwordConfirmIcon');
    const passwordMatch = document.getElementById('passwordMatch');
    const previewBtn = document.getElementById('previewBtn');
    const previewCard = document.getElementById('previewCard');
    const submitBtn = document.getElementById('submitBtn');
    const roleCheckboxes = document.querySelectorAll('.role-checkbox');

    // Éléments d'aperçu
    const previewName = document.getElementById('previewName');
    const previewEmail = document.getElementById('previewEmail');
    const previewRoles = document.getElementById('previewRoles');

    // ===== TOGGLE VISIBILITÉ MOT DE PASSE =====
    togglePasswordBtn.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        passwordIcon.classList.toggle('fa-eye');
        passwordIcon.classList.toggle('fa-eye-slash');
    });

    togglePasswordConfirmBtn.addEventListener('click', function() {
        const type = passwordConfirmInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordConfirmInput.setAttribute('type', type);
        passwordConfirmIcon.classList.toggle('fa-eye');
        passwordConfirmIcon.classList.toggle('fa-eye-slash');
    });

    // ===== VALIDATION MOT DE PASSE EN TEMPS RÉEL =====
    function checkPasswordMatch() {
        const password = passwordInput.value;
        const confirmPassword = passwordConfirmInput.value;

        if (confirmPassword === '') {
            passwordMatch.innerHTML = '';
            return;
        }

        if (password === confirmPassword) {
            passwordMatch.innerHTML = '<small class="text-success"><i class="fas fa-check-circle me-1"></i>Les mots de passe correspondent</small>';
            passwordConfirmInput.classList.remove('is-invalid');
            passwordConfirmInput.classList.add('is-valid');
        } else {
            passwordMatch.innerHTML = '<small class="text-danger"><i class="fas fa-times-circle me-1"></i>Les mots de passe ne correspondent pas</small>';
            passwordConfirmInput.classList.remove('is-valid');
            passwordConfirmInput.classList.add('is-invalid');
        }
    }

    passwordInput.addEventListener('input', checkPasswordMatch);
    passwordConfirmInput.addEventListener('input', checkPasswordMatch);

    // ===== VALIDATION FORCE MOT DE PASSE =====
    passwordInput.addEventListener('input', function() {
        const password = this.value;
        const strength = calculatePasswordStrength(password);
        updatePasswordStrengthIndicator(strength);
    });

    function calculatePasswordStrength(password) {
        let strength = 0;
        if (password.length >= 8) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        return strength;
    }

    function updatePasswordStrengthIndicator(strength) {
        const indicator = passwordInput.parentElement.parentElement.querySelector('.password-strength');
        if (indicator) indicator.remove();

        if (passwordInput.value.length > 0) {
            const strengthDiv = document.createElement('div');
            strengthDiv.className = 'password-strength mt-1';

            let strengthText = '';
            let strengthClass = '';

            switch(strength) {
                case 0:
                case 1:
                    strengthText = 'Très faible';
                    strengthClass = 'text-danger';
                    break;
                case 2:
                    strengthText = 'Faible';
                    strengthClass = 'text-warning';
                    break;
                case 3:
                    strengthText = 'Moyen';
                    strengthClass = 'text-info';
                    break;
                case 4:
                    strengthText = 'Fort';
                    strengthClass = 'text-success';
                    break;
                case 5:
                    strengthText = 'Très fort';
                    strengthClass = 'text-success fw-bold';
                    break;
            }

            strengthDiv.innerHTML = `<small class="${strengthClass}"><i class="fas fa-shield-alt me-1"></i>Force: ${strengthText}</small>`;
            passwordInput.parentElement.parentElement.appendChild(strengthDiv);
        }
    }

    // ===== APERÇU EN TEMPS RÉEL =====
    function updatePreview() {
        // Nom
        previewName.textContent = nameInput.value || 'Nom de l\'utilisateur';

        // Email
        previewEmail.textContent = emailInput.value || '<EMAIL>';

        // Rôles
        const selectedRoles = Array.from(roleCheckboxes)
            .filter(checkbox => checkbox.checked)
            .map(checkbox => checkbox.value);

        previewRoles.innerHTML = '';
        if (selectedRoles.length > 0) {
            selectedRoles.forEach(role => {
                const badge = document.createElement('span');
                badge.className = 'badge bg-primary me-1 mb-1';
                badge.innerHTML = `<i class="fas fa-user-tag me-1"></i>${role.charAt(0).toUpperCase() + role.slice(1)}`;
                previewRoles.appendChild(badge);
            });
        } else {
            previewRoles.innerHTML = '<span class="text-muted">Aucun rôle sélectionné</span>';
        }
    }

    // Écouter les changements pour l'aperçu
    nameInput.addEventListener('input', updatePreview);
    emailInput.addEventListener('input', updatePreview);
    roleCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updatePreview);
    });

    // ===== BOUTON APERÇU =====
    previewBtn.addEventListener('click', function() {
        updatePreview();
        previewCard.classList.toggle('d-none');

        if (!previewCard.classList.contains('d-none')) {
            previewCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
            this.innerHTML = '<i class="fas fa-eye-slash me-2"></i>Masquer l\'aperçu';
        } else {
            this.innerHTML = '<i class="fas fa-eye me-2"></i>Aperçu';
        }
    });

    // ===== VALIDATION FORMULAIRE =====
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Vérifier que tous les champs requis sont remplis
        let isValid = true;
        const requiredFields = [nameInput, emailInput, passwordInput, passwordConfirmInput];

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        // Vérifier qu'au moins un rôle est sélectionné
        const hasSelectedRole = Array.from(roleCheckboxes).some(checkbox => checkbox.checked);
        if (!hasSelectedRole) {
            alert('Veuillez sélectionner au moins un rôle pour l\'utilisateur.');
            isValid = false;
        }

        // Vérifier que les mots de passe correspondent
        if (passwordInput.value !== passwordConfirmInput.value) {
            passwordConfirmInput.classList.add('is-invalid');
            isValid = false;
        }

        if (isValid) {
            // Afficher l'état de chargement
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoading = submitBtn.querySelector('.btn-loading');

            btnText.classList.add('d-none');
            btnLoading.classList.remove('d-none');
            submitBtn.disabled = true;

            // Soumettre le formulaire
            setTimeout(() => {
                this.submit();
            }, 500);
        } else {
            // Faire défiler vers le premier champ invalide
            const firstInvalid = form.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstInvalid.focus();
            }
        }
    });

    // ===== ANIMATIONS D'ENTRÉE =====
    const cards = document.querySelectorAll('.modern-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // ===== VALIDATION EMAIL EN TEMPS RÉEL =====
    emailInput.addEventListener('input', function() {
        const email = this.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (email && !emailRegex.test(email)) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });

    // ===== INITIALISATION =====
    updatePreview();

    // Ajouter des effets de focus améliorés
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'translateY(-2px)';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'translateY(0)';
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin_minimal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/admin/users/create.blade.php ENDPATH**/ ?>